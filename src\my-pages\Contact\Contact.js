// Contact.js
// Cleaned + UI-fixed Contact page using Chakra UI + Formik + Yup
// - Removed unused @heroicons/react import (was causing build error)
// - Moved hooks (useMediaQuery, useFormik) *inside* component to satisfy React rules of hooks
// - Added validation schema (first/last/email/mobile/message)
// - Added safe submit handler with toast feedback + reset
// - Improved responsive layout spacing + consistent icon usage
// - Added accessible form labels + aria- attributes where helpful
// - Added type checking around window (SSR safe)
// - Ensured tel/mail links are valid
// - Provided website link icon (MdLanguage) instead of reusing MdEmail

"use client";

import React from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import {
  useToast,
  Box,
  Flex,
  Heading,
  Text,
  Input,
  Textarea,
  Button,
  FormControl,
  FormLabel,
  FormErrorMessage,
  useMediaQuery,
  Stack,
  Icon,
  chakra,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
} from "@chakra-ui/react";
import { MdLocationOn, MdEmail, MdPhone, MdLanguage } from "react-icons/md";
import Layout from "../../layout/default";
import { IoArrowBackCircleOutline } from "react-icons/io5";
import { useNavigate } from "react-router-dom";

// ------------------------------------------------------------
// Validation Schema
// ------------------------------------------------------------
const ContactSchema = Yup.object({
  firstName: Yup.string().trim().required("First name is required"),
  lastName: Yup.string().trim().required("Last name is required"),
  email: Yup.string().email("Invalid email").required("Email is required"),
  mobile: Yup.string()
    .matches(/^[0-9+\-()\s]{7,20}$/i, "Invalid phone number")
    .required("Phone number is required"),
  message: Yup.string().trim().required("Message is required"),
});

function Contact() {
  const toast = useToast();
  const [isMobile] = useMediaQuery("(max-width: 768px)");
  const navigate = useNavigate();
  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      mobile: "",
      message: "",
    },
    validationSchema: ContactSchema,
    onSubmit: async (values, actions) => {
      try {
        const myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");

        const raw = JSON.stringify({ ...values, userType: "academy" });
        const requestOptions = {
          method: "POST",
          headers: myHeaders,
          body: raw,
          redirect: "follow",
        };

        const response = await fetch(`${process.env.REACT_APP_BASE_URL}/api/contactUs`, requestOptions);
        const result = await response.json();
        if (result.status === 200) {
          toast({
            title: "Message sent!",
            description: "We will get back to you soon.",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          actions.resetForm();
        } else {
          toast({
            title: "Unable to send message.",
            description: result.message || "Please try again later.",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      } catch (err) {
        console.error("Contact form error", err);
        toast({
          title: "Unable to send message.",
          description: "Please try again later.",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } finally {
        actions.setSubmitting(false);
      }
    },
  });

  // Address multi-line helper
  const Address = chakra("span", {
    baseStyle: {
      display: "block",
      whiteSpace: "pre-line",
    },
  });

  return (
    <Layout title="Contact Us">
      <Flex alignItems="center" gap={0} mb={{base: 3, md: 0}}>
        <Button
          variant="ghost"
          size={{ base: "xs", md: "sm" }}
          leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
          onClick={() => navigate(-1)}
          _hover={{ bg: "gray.100" }}
          color="gray.700"
          fontWeight="bold"
          className="p-0"
        >
        </Button>
        <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink href="#">Contact</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>
      </Flex>
      <Flex
        direction={isMobile ? "column" : "row"}
        gap={8}
        p={isMobile ? 1 : 8}
        bg="white"
        minH="80vh"
      >
        {/* Left column: contact details */}
        <Box flex={1} mb={isMobile ? 8 : 0} maxW="600px">
          <Heading size="lg" mb={4} color="gray.800">
            Get in touch
          </Heading>
          <Text fontSize="md" color="gray.600" mb={6} lineHeight={1.5}>
            We value your feedback and are committed to providing you with the best possible experience. Get in touch today and take the first step towards reaching your goals!
          </Text>
          <Stack spacing={5} fontSize="md">
            <Flex align="center" gap={3} color="gray.700">
              <Icon as={MdLocationOn} boxSize={6} color="gray.400" />
              <Address>
                Umn Khel Shiksha Private Limited, Vasant Vihar,\nBasant Lok Complex, Road 21, New Delhi-110057
              </Address>
            </Flex>
            <Flex align="center" gap={3} color="gray.700">
              <Icon as={MdPhone} boxSize={6} color="gray.400" />
              <Text as="a" href="tel:+919267986189">
                +91 92679 86189
              </Text>
            </Flex>
            <Flex align="center" gap={3} color="gray.700">
              <Icon as={MdEmail} boxSize={6} color="gray.400" />
              <Text as="a" href="mailto:<EMAIL>">
                <EMAIL>
              </Text>
            </Flex>
            <Flex align="center" gap={3} color="gray.700">
              <Icon as={MdLanguage} boxSize={6} color="gray.400" />
              <Text
                as="a"
                href="https://khelcoach.com"
                target="_blank"
                rel="noopener noreferrer"
              >
                khelcoach.com
              </Text>
            </Flex>
          </Stack>
        </Box>

        {/* Right column: contact form */}
        <Box flex={1} maxW="600px">
          <Box bg="gray.50" p={isMobile ? 4 : 8} borderRadius="lg" boxShadow="md">
            <form onSubmit={formik.handleSubmit} noValidate>
              <Stack spacing={4}>
                <Flex gap={4} direction={isMobile ? "column" : "row"}>
                  <FormControl
                    isInvalid={formik.touched.firstName && !!formik.errors.firstName}
                  >
                    <FormLabel htmlFor="firstName">First Name</FormLabel>
                    <Input
                      id="firstName"
                      type="text"
                      name="firstName"
                      value={formik.values.firstName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter first name"
                      autoComplete="given-name"
                    />
                    <FormErrorMessage>{formik.errors.firstName}</FormErrorMessage>
                  </FormControl>
                  <FormControl
                    isInvalid={formik.touched.lastName && !!formik.errors.lastName}
                  >
                    <FormLabel htmlFor="lastName">Last Name</FormLabel>
                    <Input
                      id="lastName"
                      type="text"
                      name="lastName"
                      value={formik.values.lastName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter last name"
                      autoComplete="family-name"
                    />
                    <FormErrorMessage>{formik.errors.lastName}</FormErrorMessage>
                  </FormControl>
                </Flex>

                <FormControl isInvalid={formik.touched.email && !!formik.errors.email}>
                  <FormLabel htmlFor="email">Email</FormLabel>
                  <Input
                    id="email"
                    type="email"
                    name="email"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter email"
                    autoComplete="email"
                  />
                  <FormErrorMessage>{formik.errors.email}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={formik.touched.mobile && !!formik.errors.mobile}>
                  <FormLabel htmlFor="mobile">Phone Number</FormLabel>
                  <Input
                    id="mobile"
                    type="tel"
                    name="mobile"
                    value={formik.values.mobile}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter phone number"
                    autoComplete="tel"
                  />
                  <FormErrorMessage>{formik.errors.mobile}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={formik.touched.message && !!formik.errors.message}>
                  <FormLabel htmlFor="message">Message</FormLabel>
                  <Textarea
                    id="message"
                    name="message"
                    value={formik.values.message}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter your message"
                    rows={4}
                  />
                  <FormErrorMessage>{formik.errors.message}</FormErrorMessage>
                </FormControl>

                <Button
                  type="submit"
                  colorScheme="telegram"
                  isLoading={formik.isSubmitting}
                  w="full"
                  mt={2}
                >
                  {formik.isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </Stack>
            </form>
          </Box>
        </Box>
      </Flex>
    </Layout>
  );
}

export default Contact;
