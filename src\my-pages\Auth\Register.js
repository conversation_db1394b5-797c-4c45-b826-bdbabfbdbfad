import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Input,
  Grid,
  Select,
  Stack,
  Heading,
  Avatar,
  useToast,
  IconButton,
  Image,
  Tag,
  TagLabel,
  TagCloseButton,
  Divider,
  Text,
  InputGroup,
  InputRightElement,
  VStack,
  Checkbox,
  CheckboxGroup,
  Icon,
} from "@chakra-ui/react";
import LogoImage from "../../assets/images/mask/MainKhelCoach.svg";
import { FiPlus, FiTrash, FiChevronDown } from "react-icons/fi";
import { useNavigate } from "react-router-dom";
import { uploadImage } from "../../utilities/uploadImage";
import { State } from "country-state-city";
import axios from "axios";
import { useFormik } from "formik";
import * as Yup from "yup";
import { IoArrowBackCircleOutline } from "react-icons/io5";

// Validation schema using Yup
const validationSchema = Yup.object({
  academyName: Yup.string()
    .min(2, "Academy name must be at least 2 characters")
    .max(100, "Academy name cannot exceed 100 characters")
    .required("Academy name is required"),
  email: Yup.string()
    .email("Please enter a valid email address")
    .required("Email is required"),
  password: Yup.string()
    .min(8, "Password must be at least 8 characters")
    .required("Password is required"),
  confirmPassword: Yup.string()
    .required("Confirm password is required")
    .oneOf([Yup.ref("password"), null], "Passwords must match"),
  phone: Yup.string()
    .matches(/^\d{10}$/, "Phone number must be exactly 10 digits")
    .required("Phone number is required"),
  companyRegNo: Yup.string().required(
    "Company registration number is required"
  ),
  // Office Address Fields
  officeAddress: Yup.string().required("Office address line 1 is required"),
  officeAddressLine2: Yup.string(),
  city: Yup.string().required("City is required"),
  state: Yup.string().required("State is required"),
  pinCode: Yup.string()
    .matches(/^\d{6}$/, "Pin code must be exactly 6 digits")
    .required("Pin code is required"),
  country: Yup.string().required("Country is required"),
  // Facility Addresses
  facilityAddresses: Yup.array().of(
    Yup.object({
      name: Yup.string().required("Facility name is required"),
      addressLine1: Yup.string().required(
        "Facility address line 1 is required"
      ),
      addressLine2: Yup.string(),
      city: Yup.string().required("City is required"),
      state: Yup.string().required("State is required"),
      pinCode: Yup.string()
        .matches(/^\d{6}$/, "Pin code must be exactly 6 digits")
        .required("Pin code is required"),
      country: Yup.string().required("Country is required"),
      amenities: Yup.string(),
    })
  ),
  // Document Fields
  panNumber: Yup.string()
    .matches(
      /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
      "PAN number must be in format **********"
    )
    .required("PAN number is required"),
panImage: Yup.array()
  .of(Yup.string().required("Each PAN image is required"))
  .min(1, " PAN images are required")
  .required("PAN images are required"),

aadhaarImage: Yup.array()
  .of(Yup.string().required("Each Aadhaar image is required"))
  .min(2, "Both front and back Aadhaar images are required")
  .required("Aadhaar images are required"),
 
  aadhaarNumber: Yup.string()
    .matches(/^\d{12}$/, "Aadhaar number must be exactly 12 digits")
    .required("Aadhaar number is required"),
  // Bank Details
  accountNo: Yup.string()
    .min(9, "Account number must be at least 9 digits")
    .max(18, "Account number cannot exceed 18 digits")
    .matches(/^\d+$/, "Account number must contain only digits")
    .required("Account number is required"),
  ifsc: Yup.string()
    .matches(
      /^[A-Z]{4}0[A-Z0-9]{6}$/,
      "IFSC code must be in format ABCD0123456"
    )
    .required("IFSC code is required"),
  gstNumber: Yup.string().matches(
    /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
    "GST number must be in valid format"
  ).required("GST number is required"),
  selectedCategories: Yup.array()
    .min(1, "Please select at least one sports category")
    .required("Please select at least one sports category"),
});

const Register = () => {
  const [profileImage, setProfileImage] = useState(null);
  const [panFiles, setPanFiles] = useState({ front: null, back: null });
  const [aadhaarFiles, setAadhaarFiles] = useState({ front: null, back: null });
  const [categories, setCategories] = useState([]);
  const [categoryDropdownOpen, setCategoryDropdownOpen] = useState(false);
  const [states, setStates] = useState([]);
  const [pincodeError, setPincodeError] = useState(false);
  const [facilityPincodeErrors, setFacilityPincodeErrors] = useState({});
  const [pincodeLoading, setPincodeLoading] = useState(false);
  const [facilityPincodeLoading, setFacilityPincodeLoading] = useState({});
  const [formErrors, setFormErrors] = useState({}); // Track form field errors
  const [facilityErrors, setFacilityErrors] = useState({}); // Track facility-specific errors
  const toast = useToast();
  const navigate = useNavigate();
  const categoryInputRef = useRef();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [academyImages, setAcademyImages] = useState([]);

  // Formik form management
  const formik = useFormik({
    initialValues: {
      academyName: "",
      email: "",
      password: "",
      confirmPassword: "",
      phone: "",
      companyRegNo: "",
      officeAddress: "",
      officeAddressLine2: "",
      city: "",
      state: "",
      pinCode: "",
      country: "",
      facilityAddresses: [
        {
          name: "",
          addressLine1: "",
          addressLine2: "",
          city: "",
          state: "",
          pinCode: "",
          country: "",
          amenities: "",
        },
      ],
      panNumber: "",
      panImage: [],
      aadhaarImage: [],
      accountNo: "",
      ifsc: "",
      gstNumber: "",
      aadhaarNumber: "",
      selectedCategories: [],
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      handleRegister(values);
    },
  });

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);

  useEffect(() => {
    axios
      .get(`${process.env.REACT_APP_BASE_URL}/api/category?page=1`)
      .then((res) => {
        setCategories(res.data.data || []);
      })
      .catch((err) => {
        toast({ title: "Failed to fetch categories", status: "error" });
      });
  }, [toast]);

  // Handle click outside for category dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        categoryInputRef.current &&
        !categoryInputRef.current.contains(event.target)
      ) {
        setCategoryDropdownOpen(false);
      }
    }
    if (categoryDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [categoryDropdownOpen]);

  // Pincode validation function for office address
  const getDetailsFromPincode = async (pincode) => {
    try {
      if (!pincode || pincode.length !== 6) {
        setPincodeError(true);
        return;
      }

      setPincodeLoading(true);
      setPincodeError(false);

      const details = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`,
        { timeout: 5000 }
      );

      setPincodeLoading(false);

      if (
        details?.data[0]?.Status === "Error" ||
        details?.data[0]?.Status === "404" ||
        details?.data[0]?.Message === "No records found"
      ) {
        setPincodeError(true);
        formik.setFieldValue("city", "");
        formik.setFieldValue("state", "");
        toast({
          title: "Invalid Pincode",
          description:
            "The pincode you entered is not valid. Please check and try again.",
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        return;
      } else {
        setPincodeError(false);
        const postOffice = details?.data[0]?.PostOffice[0];
        const stateData = states.find(
          (state) => state.name === postOffice?.State
        );

        formik.setFieldValue(
          "city",
          `${postOffice?.Name}, ${postOffice?.District}`
        );
        formik.setFieldValue("state", stateData?.name || postOffice?.State);
        formik.setFieldValue("country", "India");

        toast({
          title: "Location Found",
          description: `Auto-filled city and state for pincode ${pincode}`,
          status: "success",
          duration: 2000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      setPincodeLoading(false);
      console.log("Pincode lookup error:", error);
      setPincodeError(true);
      toast({
        title: "Error validating pincode",
        description:
          "Unable to validate pincode. Please check your internet connection and try again.",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  // Pincode validation function for facility addresses
  const getDetailsFromPincodeForFacility = async (pincode, facilityIndex) => {
    try {
      if (!pincode || pincode.length !== 6) {
        setFacilityPincodeErrors((prev) => ({
          ...prev,
          [facilityIndex]: true,
        }));
        return;
      }

      setFacilityPincodeLoading((prev) => ({ ...prev, [facilityIndex]: true }));
      setFacilityPincodeErrors((prev) => ({ ...prev, [facilityIndex]: false }));

      const details = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`,
        { timeout: 5000 }
      );

      setFacilityPincodeLoading((prev) => ({
        ...prev,
        [facilityIndex]: false,
      }));

      if (
        details?.data[0]?.Status === "Error" ||
        details?.data[0]?.Status === "404" ||
        details?.data[0]?.Message === "No records found"
      ) {
        setFacilityPincodeErrors((prev) => ({
          ...prev,
          [facilityIndex]: true,
        }));

        // Clear city and state for this facility
        const updatedFacilities = [...formik.values.facilityAddresses];
        updatedFacilities[facilityIndex] = {
          ...updatedFacilities[facilityIndex],
          pinCode: pincode, // Explicitly preserve the correct pincode
          city: "",
          state: "",
        };
        formik.setFieldValue("facilityAddresses", updatedFacilities);

        toast({
          title: "Invalid Pincode",
          description: `The pincode you entered for facility ${
            facilityIndex + 1
          } is not valid. Please check and try again.`,
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        return;
      } else {
        setFacilityPincodeErrors((prev) => ({
          ...prev,
          [facilityIndex]: false,
        }));
        const postOffice = details?.data[0]?.PostOffice[0];
        const stateData = states.find(
          (state) => state.name === postOffice?.State
        );

        // Update city and state for this facility
        const updatedFacilities = [...formik.values.facilityAddresses];
        updatedFacilities[facilityIndex] = {
          ...updatedFacilities[facilityIndex],
          pinCode: pincode, // Explicitly preserve the correct pincode
          city: `${postOffice?.Name}, ${postOffice?.District}`,
          state: stateData?.name || postOffice?.State,
          country: "India",
        };
        formik.setFieldValue("facilityAddresses", updatedFacilities);

        toast({
          title: "Location Found",
          description: `Auto-filled city and state for facility ${
            facilityIndex + 1
          } pincode ${pincode}`,
          status: "success",
          duration: 2000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      setFacilityPincodeLoading((prev) => ({
        ...prev,
        [facilityIndex]: false,
      }));
      console.log("Pincode lookup error:", error);
      setFacilityPincodeErrors((prev) => ({ ...prev, [facilityIndex]: true }));
      toast({
        title: "Error validating facility pincode",
        description: `Unable to validate pincode for facility ${
          facilityIndex + 1
        }. Please check your internet connection and try again.`,
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const handleRegister = async (values) => {
    console.log("values", values);
    // Show toast if no sports category is selected
      const errors = await formik.validateForm(values);
  if (Object.keys(errors).length > 0) {
    toast({
      title: "Form Validation Error",
      description: "Please fill all required fields properly",
      status: "error",
      duration: 3000,
      position: "top",
      isClosable: true,
    });
    return; // Don't proceed with submission
  }
    if (!values.selectedCategories || values.selectedCategories.length === 0) {
      toast({
        title: "Sports Category Required",
        description:
          "Please select at least one sports category before registering.",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
      return;
    }
    // Show toast if no profile image is selected
    if (!profileImage) {
      toast({
        title: "Profile Image Required",
        description: "Please upload a profile image before registering.",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
      return;
    }
    if(!values.panImage){
      toast({
        title: "PAN Image Required",
        description: "Please upload a PAN image before registering.",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
      return;
    }
    if(!values.aadhaarImage){
      toast({
        title: "Aadhaar Image Required",
        description: "Please upload a Aadhaar image before registering.",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
      return;
    }
    try {
      // Upload profile image if present
      let profileImageUrl = null;
      if (profileImage) {
        const res = await uploadImage(profileImage);
        profileImageUrl = res.url || res;
      }

      // Upload PAN images (front and back)
      let panImageUrls = [];
      if (panFiles.front) {
        const res = await uploadImage(panFiles.front);
        panImageUrls.push(res.url || res);
      }
      if (panFiles.back) {
        const res = await uploadImage(panFiles.back);
        panImageUrls.push(res.url || res);
      }
      if (panImageUrls.length === 0) {
        panImageUrls = ["https://example.com/pan.jpg"];
      }

      // Upload Aadhaar images (front and back)
      let aadhaarImageUrls = [];
      if (aadhaarFiles.front) {
        const res = await uploadImage(aadhaarFiles.front);
        aadhaarImageUrls.push(res.url || res);
      }
      if (aadhaarFiles.back) {
        const res = await uploadImage(aadhaarFiles.back);
        aadhaarImageUrls.push(res.url || res);
      }

      // --- Academy Images Upload ---
      let uploadedAcademyImages = [];
      if (academyImages && academyImages.length > 0) {
        for (let img of academyImages) {
          if (typeof img === "string" && img.startsWith("http")) {
            uploadedAcademyImages.push(img);
          } else {
            try {
              const url = await uploadImage(img);
              uploadedAcademyImages.push(url.url || url);
            } catch (err) {
              toast({
                title: "Image Upload Failed",
                description:
                  "One or more academy images could not be uploaded.",
                status: "error",
                duration: 3000,
                position: "top",
                isClosable: true,
              });
              return;
            }
          }
        }
      }

      // Map selectedCategories (IDs) to names
      const sportsCategories = formik.values.selectedCategories
        .map((cid) => categories.find((cat) => cat._id === cid)?.name)
        .filter(Boolean);

      // Prepare office address with required fields and optional fields only if not empty

      // Find ISO code for office state
      const officeStateObj = states.find((s) => s.name === values.state);
      const officeAddress = {
        addressLine1: values.officeAddress,
        city: values.city,
        state: officeStateObj ? officeStateObj.isoCode : "",
        pinCode: values.pinCode,
        country: values.country,
      };

      // Only include addressLine2 if it's not empty
      if (
        values.officeAddressLine2 &&
        values.officeAddressLine2.trim() !== ""
      ) {
        officeAddress.addressLine2 = values.officeAddressLine2;
      }

      // Prepare linkedFacilities with required fields and optional fields only if not empty
      const linkedFacilities = values.facilityAddresses.map((addr) => {
        const facility = {
          name: addr.name,
          addressLine1: addr.addressLine1,
          city: addr.city,
          state: states.find((s) => s.name === addr.state)?.isoCode || "",
          pinCode: addr.pinCode,
          country: addr.country,
          location: {
            type: "Point",
            coordinates: [77.5946, 12.9716], // Example coordinates
            is_location_exact: true,
          },
        };
        // Only include addressLine2 if it's not empty
        if (addr.addressLine2 && addr.addressLine2.trim() !== "") {
          facility.addressLine2 = addr.addressLine2;
        }
        // Only include amenities if it's not empty
        if (addr.amenities && addr.amenities.trim() !== "") {
          facility.amenities = addr.amenities;
        }
        return facility;
      });

      // Prepare bank details
      const bankDetails = {
        accountNumber: values.accountNo,
        accountHolderName: values.academyName,
        ifsc: values.ifsc,
      };

      // Prepare payload with required fields
      const payload = {
        name: values.academyName,
        mobile: values.phone,
        email: values.email,
        gstNumber: values.gstNumber,
        sportsCategories,
        officeAddress,
        companyRegistrationNumber: values.companyRegNo,
        linkedFacilities,
        password: values.password,
        bankDetails,
        panNumber: values.panNumber,
        panImage: panImageUrls,
        aadhaarNumber: values.aadhaarNumber,
      };
      // Only include profileImage if it was uploaded
      if (profileImageUrl) {
        payload.profileImage = profileImageUrl;
      }
      // Only include aadhaarImage if any were uploaded
      if (aadhaarImageUrls.length > 0) {
        payload.aadhaarImage = aadhaarImageUrls;
      }
      // Only include academyImages if any were uploaded
      if (uploadedAcademyImages.length > 0) {
        payload.academyImages = uploadedAcademyImages;
      }

      // Get Bearer token from sessionStorage
      const token = sessionStorage.getItem("admintoken");

      // Make API call
      await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/academy`,
        payload,
        {
          headers: {
            Authorization: token,
            "Content-Type": "application/json",
          },
        }
      );

      toast({ title: "Registration successful!", status: "success" });
      navigate("/verification-pending");
    } catch (err) {
      console.log("Academy Registration API Error:", err);

      // Enhanced error handling with field highlighting
      let errorMessage = "Registration failed";
      let errorTitle = "Registration Failed";

      if (err.response) {
        // Server responded with error status
        const responseData = err.response.data;

        // Handle backend validation errors with field highlighting
        if (responseData?.errors && Array.isArray(responseData.errors)) {
          errorTitle = "Validation Error";

          // Set field errors for highlighting
          const fieldErrors = {};
          const facilityFieldErrors = {};
          const errorMessages = [];

          responseData.errors.forEach((errorItem) => {
            // Handle errors with message (field can be null for general validation errors)
            if (errorItem.message) {
              // Handle errors with specific fields
              if (errorItem.field) {
                // Handle main form field errors
                if (errorItem.field.startsWith("officeAddress.")) {
                  // Map office address fields
                  const fieldName = errorItem.field.replace(
                    "officeAddress.",
                    ""
                  );
                  if (fieldName === "addressLine1") {
                    fieldErrors["officeAddress"] = errorItem.message;
                  } else if (fieldName === "addressLine2") {
                    fieldErrors["officeAddressLine2"] = errorItem.message;
                  } else {
                    fieldErrors[fieldName] = errorItem.message;
                  }
                  errorMessages.push(
                    `Office Address - ${fieldName}: ${errorItem.message}`
                  );
                }
                // Handle facility field errors
                else if (errorItem.field.startsWith("linkedFacilities.")) {
                  // Extract facility index and field name
                  const facilityMatch = errorItem.field.match(
                    /linkedFacilities\.(\d+)\.(.+)/
                  );
                  if (facilityMatch) {
                    const facilityIndex = parseInt(facilityMatch[1]);
                    const facilityFieldName = facilityMatch[2];

                    // Store facility errors by index and field
                    if (!facilityFieldErrors[facilityIndex]) {
                      facilityFieldErrors[facilityIndex] = {};
                    }
                    facilityFieldErrors[facilityIndex][facilityFieldName] =
                      errorItem.message;

                    errorMessages.push(
                      `Facility ${facilityIndex + 1} - ${facilityFieldName}: ${
                        errorItem.message
                      }`
                    );
                  } else {
                    errorMessages.push(
                      `${errorItem.field}: ${errorItem.message}`
                    );
                  }
                }
                // Handle other form field errors
                else {
                  // Map common field names
                  let formFieldName = errorItem.field;
                  if (errorItem.field === "mobile") {
                    formFieldName = "phone";
                  } else if (errorItem.field === "name") {
                    formFieldName = "academyName";
                  } else if (errorItem.field === "companyRegistrationNumber") {
                    formFieldName = "companyRegNo";
                  } else if (errorItem.field === "accountNumber") {
                    formFieldName = "accountNo";
                  } else if (errorItem.field === "panNumber") {
                    formFieldName = "panNumber";
                  } else if (errorItem.field === "aadhaarNumber") {
                    formFieldName = "aadhaarNumber";
                  }

                  fieldErrors[formFieldName] = errorItem.message;
                  errorMessages.push(
                    `${errorItem.field}: ${errorItem.message}`
                  );
                }
              }
              // Handle general validation errors (field is null)
              else {
                // These are general validation errors that don't map to specific fields
                errorMessages.push(errorItem.message);
              }
            }
          });

          // Set form field errors for highlighting
          if (Object.keys(fieldErrors).length > 0) {
            setFormErrors(fieldErrors);
          }

          // Set facility errors for highlighting
          if (Object.keys(facilityFieldErrors).length > 0) {
            setFacilityErrors(facilityFieldErrors);
          }

          errorMessage = errorMessages.join(", ");
        } else if (
          responseData?.details &&
          Array.isArray(responseData.details)
        ) {
          errorTitle = "Validation Error";
          errorMessage = responseData.details
            .map((detail) => detail.message)
            .join(", ");
        } else if (responseData?.message) {
          errorMessage = responseData.message;
        } else {
          errorMessage = `Server error: ${err.response.status}`;
        }
      } else if (err.request) {
        // Request was made but no response received
        errorMessage = "Network error: Unable to connect to server";
      }

    

      // Reset form submission state
      formik.setSubmitting(false);
    }
  };

  const handleFacilityAddressChange = (index, value) => {
    const updated = [...formik.values.facilityAddresses];
    updated[index] = value;
    formik.setFieldValue("facilityAddresses", updated);
  };

  const addFacilityAddress = () => {
    const newFacility = {
      name: "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      state: "",
      pinCode: "",
      country: "",
      amenities: "",
    };
    formik.setFieldValue("facilityAddresses", [
      ...formik.values.facilityAddresses,
      newFacility,
    ]);
  };

  const removeFacilityAddress = (index) => {
    const updated = formik.values.facilityAddresses.filter(
      (_, i) => i !== index
    );
    // Also remove any pincode errors for this facility
    const updatedErrors = { ...facilityPincodeErrors };
    delete updatedErrors[index];
    // Adjust error keys for remaining facilities
    const adjustedErrors = {};
    Object.keys(updatedErrors).forEach((key) => {
      const keyIndex = parseInt(key);
      if (keyIndex > index) {
        adjustedErrors[keyIndex - 1] = updatedErrors[key];
      } else {
        adjustedErrors[key] = updatedErrors[key];
      }
    });
    setFacilityPincodeErrors(adjustedErrors);
    formik.setFieldValue("facilityAddresses", updated);
  };

  // Image handlers
  const handleProfileImage = (e) => {
    if (e.target.files[0]) setProfileImage(e.target.files[0]);
  };
  const removeProfileImage = () => setProfileImage(null);

 const handlePanFile = (side, e) => {
  if (e.target.files[0]) {
    setPanFiles((prev) => ({ ...prev, [side]: e.target.files[0] }));
    // Update Formik values
    const newFiles = { ...panFiles, [side]: e.target.files[0] };
    formik.setFieldValue('panImage', [newFiles.front, newFiles.back].filter(Boolean));
    formik.setFieldTouched('panImage', true);
  }
};
const removePanFile = (side) => {
  setPanFiles((prev) => ({ ...prev, [side]: null }));
  const newFiles = { ...panFiles, [side]: null };
  formik.setFieldValue('panImage', [newFiles.front, newFiles.back].filter(Boolean));
  formik.setFieldTouched('panImage', true);
};

const removeAadhaarFile = (side) => {
  setAadhaarFiles((prev) => ({ ...prev, [side]: null }));
  const newFiles = { ...aadhaarFiles, [side]: null };
  formik.setFieldValue('aadhaarImage', [newFiles.front, newFiles.back].filter(Boolean));
};

const handleAadhaarFile = (side, e) => {
  if (e.target.files[0]) {
    setAadhaarFiles((prev) => ({ ...prev, [side]: e.target.files[0] }));
    // Update Formik values
    const newFiles = { ...aadhaarFiles, [side]: e.target.files[0] };
    formik.setFieldValue('aadhaarImage', [newFiles.front, newFiles.back].filter(Boolean));
  }
};


  // Sports categories handlers
  const handleCategorySelect = (id) => {
    const prev = formik.values.selectedCategories;
    const newSelected = prev.includes(id)
      ? prev.filter((cid) => cid !== id)
      : [...prev, id];
    formik.setFieldValue("selectedCategories", newSelected);
  };
  const removeCategory = (id) => {
    const prev = formik.values.selectedCategories;
    const newSelected = prev.filter((cid) => cid !== id);
    formik.setFieldValue("selectedCategories", newSelected);
  };

  const handleShowClick = () => setShowPassword(!showPassword);
  // Academy images handlers
  const handleAcademyImagesUpload = async (e) => {
    const files = Array.from(e.target.files);

    if (files.length === 0) return;

    try {
      // Upload all selected files simultaneously
      const uploadPromises = files.map((file) => uploadImage(file));
      const uploadResults = await Promise.all(uploadPromises);

      // Extract URLs from upload results
      const imageUrls = uploadResults.map((result) => result.url || result);

      // Add new images to existing array
      setAcademyImages((prev) => [...prev, ...imageUrls]);

      // Reset input to allow re-uploading same files
      e.target.value = "";

      toast({
        title: "Images uploaded successfully",
        description: `${files.length} image(s) added to academy gallery`,
        status: "success",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    } catch (error) {
      console.error("Academy images upload error:", error);
      toast({
        title: "Image upload failed",
        description: "Please try again or check your internet connection",
        status: "error",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
      // Reset input on error
      e.target.value = "";
    }
  };

  const removeAcademyImage = (indexToRemove) => {
    setAcademyImages((prev) =>
      prev.filter((_, index) => index !== indexToRemove)
    );
    toast({
      title: "Image removed",
      description: "Image has been removed from academy gallery",
      status: "info",
      duration: 2000,
      position: "top",
      isClosable: true,
    });
  };

  // --- Registration handler ---

  return (
    <Flex
      minH="100vh"
      align="center"
      justify="center"
      bg="gray.100"
      direction="column"
      py={{ base: 4, md: 6 }}
      px={{ base: 1, sm: 6, md: 8 }}
    >
      <Image
        src={LogoImage}
        alt="logo"
        w={{ base: "140px", sm: "160px", md: "180px" }}
        mb={4}
      />
      <Box
        bg="white"
        p={{ base: 3, sm: 3, md: 8 }}
        rounded="lg"
        w={{ base: "100%", sm: "95%", md: "850px", lg: "1050px" }}
        maxW="95vw"
        boxShadow="lg"
      >
        {/* Back Button */}
        <Button
          leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
          variant="ghost"
          colorScheme="gray"
          onClick={() => navigate(-1)}
          size="sm"
          aria-label="Back"
          mb={2}
          alignSelf="flex-start"
          className="p-0"
        >
          Back
        </Button>
        {/* Heading and subheading centered */}
        <Box textAlign="center" mb={6}>
          <Heading fontSize={{ base: "xl", md: "2xl" }} m={0}>
            Academy Registration
          </Heading>
          <Text
            mb={0}
            mt={1}
            color="gray.500"
            fontSize={{ base: "sm", md: "md" }}
          >
            Fill in the details to register your academy
          </Text>
        </Box>
        <Stack spacing={6} divider={<Divider />}>
          {/* Profile Section */}
          <Flex
            gap={{ base: 4, md: 8 }}
            align="flex-start"
            direction={{ base: "column", md: "row" }}
          >
            <Box minW={{ base: "140px", md: "160px" }}>
              <FormLabel fontWeight="bold" mb={3}>
                Profile Image{" "}
                <Text as="span" color="red.500">
                  *
                </Text>
              </FormLabel>
              <VStack spacing={3} align="center">
                <Box position="relative">
                  <Avatar
                    size={{ base: "lg", md: "xl" }}
                    src={
                      profileImage
                        ? URL.createObjectURL(profileImage)
                        : undefined
                    }
                    bg={profileImage ? undefined : "gray.100"}
                    border="3px solid"
                    borderColor={profileImage ? "green.200" : "gray.200"}
                    transition="all 0.2s"
                  />
                  {profileImage && (
                    <Box
                      position="absolute"
                      top={0}
                      right={0}
                      bg="white"
                      borderRadius="full"
                      p={1}
                      boxShadow="md"
                      border="2px solid"
                      borderColor="gray.100"
                    >
                      <Icon
                        as={FiTrash}
                        boxSize={4}
                        color="red.500"
                        cursor="pointer"
                        onClick={removeProfileImage}
                        _hover={{ color: "red.600" }}
                      />
                    </Box>
                  )}
                </Box>
                <VStack spacing={2} w="full">
                  <Input
                    type="file"
                    accept="image/*"
                    display="none"
                    id="profile-image-upload"
                    onChange={handleProfileImage}
                  />
                  <Button
                    as="label"
                    htmlFor="profile-image-upload"
                    leftIcon={<FiPlus />}
                    size="sm"
                    colorScheme={profileImage ? "blue" : "teal"}
                    variant={profileImage ? "outline" : "solid"}
                    w="full"
                  >
                    {profileImage ? "Change Photo" : "Upload Photo"}
                  </Button>
                  {profileImage && (
                    <Button
                      colorScheme="red"
                      leftIcon={<FiTrash />}
                      onClick={removeProfileImage}
                      variant="outline"
                      size="sm"
                      w="full"
                    >
                      Remove Photo
                    </Button>
                  )}
                </VStack>
              </VStack>
            </Box>
            <Stack flex={1} spacing={4}>
              <FormControl
                isInvalid={
                  formik.errors.academyName && formik.touched.academyName
                }
              >
                <FormLabel fontWeight="bold">
                  Academy Name{" "}
                  <Text as="span" color="red.500">
                    *
                  </Text>
                </FormLabel>
                <Input
                  name="academyName"
                  value={formik.values.academyName}
                  placeholder="Enter academy name *"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  borderColor={
                    (formik.errors.academyName && formik.touched.academyName) ||
                    formErrors.academyName
                      ? "red.300"
                      : "gray.200"
                  }
                  _hover={{
                    borderColor:
                      (formik.errors.academyName &&
                        formik.touched.academyName) ||
                      formErrors.academyName
                        ? "red.400"
                        : "gray.300",
                  }}
                  _focus={{
                    borderColor:
                      (formik.errors.academyName &&
                        formik.touched.academyName) ||
                      formErrors.academyName
                        ? "red.500"
                        : "blue.500",
                    shadow:
                      (formik.errors.academyName &&
                        formik.touched.academyName) ||
                      formErrors.academyName
                        ? "0 0 0 1px var(--chakra-colors-red-500)"
                        : "0 0 0 1px var(--chakra-colors-blue-500)",
                  }}
                />
                <FormErrorMessage>{formik.errors.academyName}</FormErrorMessage>
                {formErrors.academyName && !formik.errors.academyName && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {formErrors.academyName}
                  </Text>
                )}
              </FormControl>
              <FormControl
                isInvalid={formik.errors.email && formik.touched.email}
              >
                <FormLabel fontWeight="bold">
                  Email{" "}
                  <Text as="span" color="red.500">
                    *
                  </Text>
                </FormLabel>
                <Input
                  name="email"
                  type="email"
                  value={formik.values.email}
                  placeholder="Email address *"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  borderColor={
                    (formik.errors.email && formik.touched.email) ||
                    formErrors.email
                      ? "red.300"
                      : "gray.200"
                  }
                  _hover={{
                    borderColor:
                      (formik.errors.email && formik.touched.email) ||
                      formErrors.email
                        ? "red.400"
                        : "gray.300",
                  }}
                  _focus={{
                    borderColor:
                      (formik.errors.email && formik.touched.email) ||
                      formErrors.email
                        ? "red.500"
                        : "blue.500",
                    shadow:
                      (formik.errors.email && formik.touched.email) ||
                      formErrors.email
                        ? "0 0 0 1px var(--chakra-colors-red-500)"
                        : "0 0 0 1px var(--chakra-colors-blue-500)",
                  }}
                />
                <FormErrorMessage>{formik.errors.email}</FormErrorMessage>
                {formErrors.email && !formik.errors.email && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {formErrors.email}
                  </Text>
                )}
              </FormControl>
            </Stack>
          </Flex>

          {/* Account Details */}
          <Stack spacing={4} direction={{ base: "column", md: "row" }}>
            <FormControl
              isInvalid={formik.errors.password && formik.touched.password}
              flex={1}
            >
              <FormLabel fontWeight="bold">
                Password{" "}
                <Text as="span" color="red.500">
                  *
                </Text>
              </FormLabel>
              <InputGroup>
                <Input
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formik.values.password}
                  placeholder="Password *"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  borderColor={
                    (formik.errors.password && formik.touched.password) ||
                    formErrors.password
                      ? "red.300"
                      : "gray.200"
                  }
                  _hover={{
                    borderColor:
                      (formik.errors.password && formik.touched.password) ||
                      formErrors.password
                        ? "red.400"
                        : "gray.300",
                  }}
                  _focus={{
                    borderColor:
                      (formik.errors.password && formik.touched.password) ||
                      formErrors.password
                        ? "red.500"
                        : "blue.500",
                    shadow:
                      (formik.errors.password && formik.touched.password) ||
                      formErrors.password
                        ? "0 0 0 1px var(--chakra-colors-red-500)"
                        : "0 0 0 1px var(--chakra-colors-blue-500)",
                  }}
                />
                <InputRightElement width="4.5rem">
                  <Button h="1.75rem" size="sm" onClick={handleShowClick}>
                    {showPassword ? "Hide" : "Show"}
                  </Button>
                </InputRightElement>
              </InputGroup>
              <FormErrorMessage>{formik.errors.password}</FormErrorMessage>
              {formErrors.password && !formik.errors.password && (
                <Text color="red.500" fontSize="sm" mt={1}>
                  {formErrors.password}
                </Text>
              )}
            </FormControl>
            <FormControl
              isInvalid={
                formik.errors.confirmPassword && formik.touched.confirmPassword
              }
              flex={1}
            >
              <FormLabel fontWeight="bold">
                Confirm Password{" "}
                <Text as="span" color="red.500">
                  *
                </Text>
              </FormLabel>
              <InputGroup>
                <Input
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={formik.values.confirmPassword}
                  placeholder="Confirm Password *"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  borderColor={
                    (formik.errors.confirmPassword &&
                      formik.touched.confirmPassword) ||
                    formErrors.confirmPassword
                      ? "red.300"
                      : "gray.200"
                  }
                  _hover={{
                    borderColor:
                      (formik.errors.confirmPassword &&
                        formik.touched.confirmPassword) ||
                      formErrors.confirmPassword
                        ? "red.400"
                        : "gray.300",
                  }}
                  _focus={{
                    borderColor:
                      (formik.errors.confirmPassword &&
                        formik.touched.confirmPassword) ||
                      formErrors.confirmPassword
                        ? "red.500"
                        : "blue.500",
                    shadow:
                      (formik.errors.confirmPassword &&
                        formik.touched.confirmPassword) ||
                      formErrors.confirmPassword
                        ? "0 0 0 1px var(--chakra-colors-red-500)"
                        : "0 0 0 1px var(--chakra-colors-blue-500)",
                  }}
                />
                <InputRightElement width="4.5rem">
                  <Button
                    h="1.75rem"
                    size="sm"
                    onClick={() => setShowConfirmPassword((v) => !v)}
                  >
                    {showConfirmPassword ? "Hide" : "Show"}
                  </Button>
                </InputRightElement>
              </InputGroup>
              <FormErrorMessage>
                {formik.errors.confirmPassword}
              </FormErrorMessage>
              {formErrors.confirmPassword && !formik.errors.confirmPassword && (
                <Text color="red.500" fontSize="sm" mt={1}>
                  {formErrors.confirmPassword}
                </Text>
              )}
            </FormControl>
            <FormControl
              isInvalid={formik.errors.phone && formik.touched.phone}
              flex={1}
            >
              <FormLabel fontWeight="bold">
                Phone Number{" "}
                <Text as="span" color="red.500">
                  *
                </Text>
              </FormLabel>
              <Input
                name="phone"
                value={formik.values.phone}
                placeholder="Phone Number *"
                onChange={(e) => {
                  formik.handleChange(e);
                  // Clear backend error when user starts typing
                  if (formErrors.phone) {
                    setFormErrors((prev) => {
                      const updated = { ...prev };
                      delete updated.phone;
                      return updated;
                    });
                  }
                }}
                onBlur={formik.handleBlur}
                borderColor={
                  (formik.errors.phone && formik.touched.phone) ||
                  formErrors.phone
                    ? "red.300"
                    : "gray.200"
                }
                _hover={{
                  borderColor:
                    (formik.errors.phone && formik.touched.phone) ||
                    formErrors.phone
                      ? "red.400"
                      : "gray.300",
                }}
                _focus={{
                  borderColor:
                    (formik.errors.phone && formik.touched.phone) ||
                    formErrors.phone
                      ? "red.500"
                      : "blue.500",
                  shadow:
                    (formik.errors.phone && formik.touched.phone) ||
                    formErrors.phone
                      ? "0 0 0 1px var(--chakra-colors-red-500)"
                      : "0 0 0 1px var(--chakra-colors-blue-500)",
                }}
              />
              <FormErrorMessage>{formik.errors.phone}</FormErrorMessage>
              {formErrors.phone && !formik.errors.phone && (
                <Text color="red.500" fontSize="sm" mt={1}>
                  {formErrors.phone}
                </Text>
              )}
            </FormControl>
          </Stack>

          {/* Company Registration Number */}
          <FormControl
            isInvalid={
              formik.errors.companyRegNo && formik.touched.companyRegNo
            }
          >
            <FormLabel fontWeight="bold">
              Company Registration Number{" "}
              <Text as="span" color="red.500">
                *
              </Text>
            </FormLabel>
            <Input
              name="companyRegNo"
              value={formik.values.companyRegNo}
              placeholder="Enter company registration number *"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              borderColor={
                (formik.errors.companyRegNo && formik.touched.companyRegNo) ||
                formErrors.companyRegNo
                  ? "red.300"
                  : "gray.200"
              }
              _hover={{
                borderColor:
                  (formik.errors.companyRegNo && formik.touched.companyRegNo) ||
                  formErrors.companyRegNo
                    ? "red.400"
                    : "gray.300",
              }}
              _focus={{
                borderColor:
                  (formik.errors.companyRegNo && formik.touched.companyRegNo) ||
                  formErrors.companyRegNo
                    ? "red.500"
                    : "blue.500",
                shadow:
                  (formik.errors.companyRegNo && formik.touched.companyRegNo) ||
                  formErrors.companyRegNo
                    ? "0 0 0 1px var(--chakra-colors-red-500)"
                    : "0 0 0 1px var(--chakra-colors-blue-500)",
              }}
            />

            <FormErrorMessage>{formik.errors.companyRegNo}</FormErrorMessage>
            {formErrors.companyRegNo && !formik.errors.companyRegNo && (
              <Text color="red.500" fontSize="sm" mt={1}>
                {formErrors.companyRegNo}
              </Text>
            )}
          </FormControl>

          {/* Office Address Section */}
          <Box>
            <FormLabel fontWeight="bold">
              Office Address{" "}
              <Text as="span" color="red.500">
                *
              </Text>
            </FormLabel>
            <VStack align="stretch" spacing={2}>
              {/* Pin Code - First for auto-population */}
              <Box>
                <Input
                  name="pinCode"
                  value={formik.values.pinCode}
                  placeholder="Enter Pin Code to auto-populate location details *"
                  onChange={(e) => {
                    const newValue = e.target.value;
                    // Only allow numeric input and limit to reasonable length
                    if (/^\d*$/.test(newValue) && newValue.length <= 8) {
                      formik.setFieldValue("pinCode", newValue);

                      // Trigger validation when user types exactly 6 digits
                      if (newValue.length === 6) {
                        getDetailsFromPincode(newValue);
                      } else if (newValue.length !== 6) {
                        // Reset error state if pincode is not 6 digits
                        setPincodeError(false);
                      }
                    }
                  }}
                  onBlur={(e) => {
                    const pincode = e.target.value.trim();
                    formik.handleBlur(e);
                    if (pincode && pincode.length === 6) {
                      getDetailsFromPincode(pincode);
                    }
                  }}
                  borderColor={
                    pincodeError ||
                    formErrors.pinCode ||
                    (formik.errors.pinCode && formik.touched.pinCode)
                      ? "red.300"
                      : pincodeLoading
                      ? "blue.500"
                      : "gray.200"
                  }
                  _hover={{
                    borderColor:
                      pincodeError ||
                      formErrors.pinCode ||
                      (formik.errors.pinCode && formik.touched.pinCode)
                        ? "red.400"
                        : pincodeLoading
                        ? "blue.600"
                        : "gray.300",
                  }}
                  _focus={{
                    borderColor:
                      pincodeError ||
                      formErrors.pinCode ||
                      (formik.errors.pinCode && formik.touched.pinCode)
                        ? "red.500"
                        : pincodeLoading
                        ? "blue.500"
                        : "blue.500",
                    shadow:
                      pincodeError ||
                      formErrors.pinCode ||
                      (formik.errors.pinCode && formik.touched.pinCode)
                        ? "0 0 0 1px var(--chakra-colors-red-500)"
                        : "0 0 0 1px var(--chakra-colors-blue-500)",
                  }}
                  bg={pincodeLoading ? "blue.50" : undefined}
                  _placeholder={{
                    color: pincodeLoading ? "blue.400" : undefined,
                  }}
                  readOnly={pincodeLoading}
                />
                {pincodeLoading && (
                  <Text color="blue.500" fontSize="sm" mt={1}>
                    🔍 Looking up location...
                  </Text>
                )}
                {(pincodeError ||
                  formErrors.pinCode ||
                  (formik.errors.pinCode && formik.touched.pinCode)) &&
                  !pincodeLoading && (
                    <Text color="red.500" fontSize="sm" mt={1}>
                      {formik.errors.pinCode ||
                        formErrors.pinCode ||
                        "Invalid pincode - please check and try again"}
                    </Text>
                  )}
              </Box>
              {/* City, State, Country Row */}
              <Stack spacing={2} direction={{ base: "column", sm: "row" }}>
                <Box flex={1}>
                  <FormLabel fontSize="sm" fontWeight="semibold" mb={1}>
                    City{" "}
                    <Text as="span" color="red.500">
                      *
                    </Text>
                  </FormLabel>
                  <Input
                    name="city"
                    value={formik.values.city}
                    placeholder="City *"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    borderColor={
                      (formik.errors.city && formik.touched.city) ||
                      formErrors.city
                        ? "red.300"
                        : "gray.200"
                    }
                    _hover={{
                      borderColor:
                        (formik.errors.city && formik.touched.city) ||
                        formErrors.city
                          ? "red.400"
                          : "gray.300",
                    }}
                    _focus={{
                      borderColor:
                        (formik.errors.city && formik.touched.city) ||
                        formErrors.city
                          ? "red.500"
                          : "blue.500",
                      shadow:
                        (formik.errors.city && formik.touched.city) ||
                        formErrors.city
                          ? "0 0 0 1px var(--chakra-colors-red-500)"
                          : "0 0 0 1px var(--chakra-colors-blue-500)",
                    }}
                  />
                  {((formik.errors.city && formik.touched.city) ||
                    formErrors.city) && (
                    <Text color="red.500" fontSize="sm" mt={1}>
                      {formik.errors.city || formErrors.city}
                    </Text>
                  )}
                </Box>
                <Box flex={1}>
                  <FormLabel fontSize="sm" fontWeight="semibold" mb={1}>
                    State{" "}
                    <Text as="span" color="red.500">
                      *
                    </Text>
                  </FormLabel>
                  <Select
                    name="state"
                    value={formik.values.state}
                    placeholder="Select State *"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    borderColor={
                      (formik.errors.state && formik.touched.state) ||
                      formErrors.state
                        ? "red.300"
                        : "gray.200"
                    }
                    _hover={{
                      borderColor:
                        (formik.errors.state && formik.touched.state) ||
                        formErrors.state
                          ? "red.400"
                          : "gray.300",
                    }}
                    _focus={{
                      borderColor:
                        (formik.errors.state && formik.touched.state) ||
                        formErrors.state
                          ? "red.500"
                          : "blue.500",
                      shadow:
                        (formik.errors.state && formik.touched.state) ||
                        formErrors.state
                          ? "0 0 0 1px var(--chakra-colors-red-500)"
                          : "0 0 0 1px var(--chakra-colors-blue-500)",
                    }}
                  >
                    {states.map((state) => (
                      <option key={state.isoCode} value={state.name}>
                        {state.name}
                      </option>
                    ))}
                  </Select>
                  {((formik.errors.state && formik.touched.state) ||
                    formErrors.state) && (
                    <Text color="red.500" fontSize="sm" mt={1}>
                      {formik.errors.state || formErrors.state}
                    </Text>
                  )}
                </Box>
                <Box flex={1}>
                  <FormLabel fontSize="sm" fontWeight="semibold" mb={1}>
                    Country{" "}
                    <Text as="span" color="red.500">
                      *
                    </Text>
                  </FormLabel>
                  <Input
                    name="country"
                    value={formik.values.country}
                    placeholder="Country *"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    borderColor={
                      (formik.errors.country && formik.touched.country) ||
                      formErrors.country
                        ? "red.300"
                        : "gray.200"
                    }
                    _hover={{
                      borderColor:
                        (formik.errors.country && formik.touched.country) ||
                        formErrors.country
                          ? "red.400"
                          : "gray.300",
                    }}
                    _focus={{
                      borderColor:
                        (formik.errors.country && formik.touched.country) ||
                        formErrors.country
                          ? "red.500"
                          : "blue.500",
                      shadow:
                        (formik.errors.country && formik.touched.country) ||
                        formErrors.country
                          ? "0 0 0 1px var(--chakra-colors-red-500)"
                          : "0 0 0 1px var(--chakra-colors-blue-500)",
                    }}
                  />
                  {((formik.errors.country && formik.touched.country) ||
                    formErrors.country) && (
                    <Text color="red.500" fontSize="sm" mt={1}>
                      {formik.errors.country || formErrors.country}
                    </Text>
                  )}
                </Box>
              </Stack>
              {/* Address Lines */}
              <Box>
                <Input
                  name="officeAddress"
                  value={formik.values.officeAddress}
                  placeholder="Office Address Line 1 *"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  borderColor={
                    (formik.errors.officeAddress &&
                      formik.touched.officeAddress) ||
                    formErrors.officeAddress
                      ? "red.300"
                      : "gray.200"
                  }
                  _hover={{
                    borderColor:
                      (formik.errors.officeAddress &&
                        formik.touched.officeAddress) ||
                      formErrors.officeAddress
                        ? "red.400"
                        : "gray.300",
                  }}
                  _focus={{
                    borderColor:
                      (formik.errors.officeAddress &&
                        formik.touched.officeAddress) ||
                      formErrors.officeAddress
                        ? "red.500"
                        : "blue.500",
                    shadow:
                      (formik.errors.officeAddress &&
                        formik.touched.officeAddress) ||
                      formErrors.officeAddress
                        ? "0 0 0 1px var(--chakra-colors-red-500)"
                        : "0 0 0 1px var(--chakra-colors-blue-500)",
                  }}
                />
                {((formik.errors.officeAddress &&
                  formik.touched.officeAddress) ||
                  formErrors.officeAddress) && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {formik.errors.officeAddress || formErrors.officeAddress}
                  </Text>
                )}
              </Box>
              <Box>
                <Input
                  name="officeAddressLine2"
                  value={formik.values.officeAddressLine2}
                  placeholder="Office Address Line 2 (Optional)"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  borderColor={
                    (formik.errors.officeAddressLine2 &&
                      formik.touched.officeAddressLine2) ||
                    formErrors.officeAddressLine2
                      ? "red.300"
                      : "gray.200"
                  }
                  _hover={{
                    borderColor:
                      (formik.errors.officeAddressLine2 &&
                        formik.touched.officeAddressLine2) ||
                      formErrors.officeAddressLine2
                        ? "red.400"
                        : "gray.300",
                  }}
                  _focus={{
                    borderColor:
                      (formik.errors.officeAddressLine2 &&
                        formik.touched.officeAddressLine2) ||
                      formErrors.officeAddressLine2
                        ? "red.500"
                        : "blue.500",
                    shadow:
                      (formik.errors.officeAddressLine2 &&
                        formik.touched.officeAddressLine2) ||
                      formErrors.officeAddressLine2
                        ? "0 0 0 1px var(--chakra-colors-red-500)"
                        : "0 0 0 1px var(--chakra-colors-blue-500)",
                  }}
                />
                {((formik.errors.officeAddressLine2 &&
                  formik.touched.officeAddressLine2) ||
                  formErrors.officeAddressLine2) && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {formik.errors.officeAddressLine2 ||
                      formErrors.officeAddressLine2}
                  </Text>
                )}
              </Box>
            </VStack>
          </Box>

          {/* Facilities Section */}
          <Box>
            <FormLabel fontWeight="bold" fontSize="lg" mb={4}>
              Facilities
            </FormLabel>
            {formik.values.facilityAddresses.map((address, idx) => (
              <Box
                key={idx}
                mt={6}
                p={{ base: 4, md: 6 }}
                borderWidth={2}
                borderRadius="lg"
                borderColor="gray.200"
                bg="gray.50"
                position="relative"
                _hover={{ borderColor: "blue.300" }}
                transition="border-color 0.2s"
              >
                {/* Facility Header */}
                <Flex
                  justify="space-between"
                  align="center"
                  mb={4}
                  direction={{ base: "column", sm: "row" }}
                  gap={{ base: 2, sm: 0 }}
                >
                  <Text
                    fontSize={{ base: "sm", md: "md" }}
                    fontWeight="semibold"
                    color="gray.700"
                  >
                    Facility {idx + 1}
                  </Text>
                  <Button
                    size="sm"
                    colorScheme="red"
                    variant="outline"
                    onClick={() => removeFacilityAddress(idx)}
                    leftIcon={<FiTrash />}
                    isDisabled={formik.values.facilityAddresses.length === 1}
                  >
                    Remove
                  </Button>
                </Flex>

                {/* Facility Form Fields */}
                <VStack spacing={4} align="stretch">
                  {/* Facility Name */}
                  <FormControl>
                    <FormLabel
                      fontWeight="semibold"
                      fontSize="sm"
                      color="gray.600"
                    >
                      Facility Name{" "}
                      <Text as="span" color="red.500">
                        *
                      </Text>
                    </FormLabel>
                    <Input
                      value={address.name || ""}
                      placeholder="Enter facility name *"
                      onChange={(e) =>
                        handleFacilityAddressChange(idx, {
                          ...address,
                          name: e.target.value,
                        })
                      }
                      bg="white"
                      borderColor={
                        facilityErrors[idx]?.name ? "red.300" : "gray.300"
                      }
                      _hover={{
                        borderColor: facilityErrors[idx]?.name
                          ? "red.400"
                          : "blue.300",
                      }}
                      _focus={{
                        borderColor: facilityErrors[idx]?.name
                          ? "red.500"
                          : "blue.500",
                        shadow: facilityErrors[idx]?.name
                          ? "0 0 0 1px var(--chakra-colors-red-500)"
                          : "0 0 0 1px var(--chakra-colors-blue-500)",
                      }}
                    />
                    {facilityErrors[idx]?.name && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {facilityErrors[idx].name}
                      </Text>
                    )}
                  </FormControl>

                  {/* Pin Code - Moved to top for auto-population */}
                  <FormControl>
                    <FormLabel
                      fontWeight="semibold"
                      fontSize="sm"
                      color="gray.600"
                    >
                      Pin Code{" "}
                      <Text as="span" color="red.500">
                        *
                      </Text>
                    </FormLabel>
                    <Input
                      value={address.pinCode || ""}
                      placeholder="Enter Pin Code to auto-populate location details *"
                      onChange={(e) => {
                        const newValue = e.target.value;
                        // Only allow numeric input and limit to reasonable length
                        if (/^\d*$/.test(newValue) && newValue.length <= 8) {
                          handleFacilityAddressChange(idx, {
                            ...address,
                            pinCode: newValue,
                          });

                          // Trigger validation when user types exactly 6 digits
                          if (newValue.length === 6) {
                            getDetailsFromPincodeForFacility(newValue, idx);
                          } else if (newValue.length !== 6) {
                            // Reset error state if pincode is not 6 digits
                            setFacilityPincodeErrors((prev) => ({
                              ...prev,
                              [idx]: false,
                            }));
                          }
                        }
                      }}
                      onBlur={(e) => {
                        const pincode = e.target.value.trim();
                        if (pincode && pincode.length === 6) {
                          getDetailsFromPincodeForFacility(pincode, idx);
                        }
                      }}
                      borderColor={
                        facilityPincodeErrors[idx] ||
                        facilityErrors[idx]?.pinCode
                          ? "red.300"
                          : facilityPincodeLoading[idx]
                          ? "blue.500"
                          : "gray.300"
                      }
                      _hover={{
                        borderColor:
                          facilityPincodeErrors[idx] ||
                          facilityErrors[idx]?.pinCode
                            ? "red.400"
                            : facilityPincodeLoading[idx]
                            ? "blue.600"
                            : "blue.300",
                      }}
                      _focus={{
                        borderColor:
                          facilityPincodeErrors[idx] ||
                          facilityErrors[idx]?.pinCode
                            ? "red.500"
                            : facilityPincodeLoading[idx]
                            ? "blue.500"
                            : "blue.500",
                        shadow:
                          facilityPincodeErrors[idx] ||
                          facilityErrors[idx]?.pinCode
                            ? "0 0 0 1px var(--chakra-colors-red-500)"
                            : "0 0 0 1px var(--chakra-colors-blue-500)",
                      }}
                      bg={facilityPincodeLoading[idx] ? "blue.50" : "white"}
                      _placeholder={{
                        color: facilityPincodeLoading[idx]
                          ? "blue.400"
                          : undefined,
                      }}
                      readOnly={facilityPincodeLoading[idx]}
                    />
                    {facilityPincodeLoading[idx] && (
                      <Text color="blue.500" fontSize="sm" mt={1}>
                        🔍 Looking up location...
                      </Text>
                    )}
                    {(facilityPincodeErrors[idx] ||
                      facilityErrors[idx]?.pinCode) &&
                      !facilityPincodeLoading[idx] && (
                        <Text color="red.500" fontSize="sm" mt={1}>
                          {facilityErrors[idx]?.pinCode ||
                            "Invalid pincode - please check and try again"}
                        </Text>
                      )}
                  </FormControl>

                  {/* City, State, Country Row */}
                  <Stack spacing={4} direction={{ base: "column", sm: "row" }}>
                    <FormControl>
                      <FormLabel
                        fontWeight="semibold"
                        fontSize="sm"
                        color="gray.600"
                      >
                        City{" "}
                        <Text as="span" color="red.500">
                          *
                        </Text>
                      </FormLabel>
                      <Input
                        value={address.city || ""}
                        placeholder="City *"
                        onChange={(e) =>
                          handleFacilityAddressChange(idx, {
                            ...address,
                            city: e.target.value,
                          })
                        }
                        bg="white"
                        borderColor={
                          facilityErrors[idx]?.city ? "red.300" : "gray.300"
                        }
                        _hover={{
                          borderColor: facilityErrors[idx]?.city
                            ? "red.400"
                            : "blue.300",
                        }}
                        _focus={{
                          borderColor: facilityErrors[idx]?.city
                            ? "red.500"
                            : "blue.500",
                          shadow: facilityErrors[idx]?.city
                            ? "0 0 0 1px var(--chakra-colors-red-500)"
                            : "0 0 0 1px var(--chakra-colors-blue-500)",
                        }}
                      />
                      {facilityErrors[idx]?.city && (
                        <Text color="red.500" fontSize="sm" mt={1}>
                          {facilityErrors[idx].city}
                        </Text>
                      )}
                    </FormControl>

                    <FormControl>
                      <FormLabel
                        fontWeight="semibold"
                        fontSize="sm"
                        color="gray.600"
                      >
                        State{" "}
                        <Text as="span" color="red.500">
                          *
                        </Text>
                      </FormLabel>
                      <Select
                        value={address.state || ""}
                        placeholder="Select State *"
                        onChange={(e) =>
                          handleFacilityAddressChange(idx, {
                            ...address,
                            state: e.target.value,
                          })
                        }
                        bg="white"
                        borderColor={
                          facilityErrors[idx]?.state ? "red.300" : "gray.300"
                        }
                        _hover={{
                          borderColor: facilityErrors[idx]?.state
                            ? "red.400"
                            : "blue.300",
                        }}
                        _focus={{
                          borderColor: facilityErrors[idx]?.state
                            ? "red.500"
                            : "blue.500",
                          shadow: facilityErrors[idx]?.state
                            ? "0 0 0 1px var(--chakra-colors-red-500)"
                            : "0 0 0 1px var(--chakra-colors-blue-500)",
                        }}
                      >
                        {states.map((state) => (
                          <option key={state.isoCode} value={state.name}>
                            {state.name}
                          </option>
                        ))}
                      </Select>
                      {facilityErrors[idx]?.state && (
                        <Text color="red.500" fontSize="sm" mt={1}>
                          {facilityErrors[idx].state}
                        </Text>
                      )}
                    </FormControl>

                    <FormControl>
                      <FormLabel
                        fontWeight="semibold"
                        fontSize="sm"
                        color="gray.600"
                      >
                        Country{" "}
                        <Text as="span" color="red.500">
                          *
                        </Text>
                      </FormLabel>
                      <Input
                        value={address.country || ""}
                        placeholder="Country *"
                        onChange={(e) =>
                          handleFacilityAddressChange(idx, {
                            ...address,
                            country: e.target.value,
                          })
                        }
                        bg="white"
                        borderColor={
                          facilityErrors[idx]?.country ? "red.300" : "gray.300"
                        }
                        _hover={{
                          borderColor: facilityErrors[idx]?.country
                            ? "red.400"
                            : "blue.300",
                        }}
                        _focus={{
                          borderColor: facilityErrors[idx]?.country
                            ? "red.500"
                            : "blue.500",
                          shadow: facilityErrors[idx]?.country
                            ? "0 0 0 1px var(--chakra-colors-red-500)"
                            : "0 0 0 1px var(--chakra-colors-blue-500)",
                        }}
                      />
                      {facilityErrors[idx]?.country && (
                        <Text color="red.500" fontSize="sm" mt={1}>
                          {facilityErrors[idx].country}
                        </Text>
                      )}
                    </FormControl>
                  </Stack>

                  {/* Address Fields */}
                  <Box>
                    <FormLabel
                      fontWeight="semibold"
                      fontSize="sm"
                      color="gray.600"
                      mb={3}
                    >
                      Address{" "}
                      <Text as="span" color="red.500">
                        *
                      </Text>
                    </FormLabel>
                    <VStack spacing={3}>
                      <Input
                        value={address.addressLine1 || ""}
                        placeholder="Address Line 1 *"
                        onChange={(e) =>
                          handleFacilityAddressChange(idx, {
                            ...address,
                            addressLine1: e.target.value,
                          })
                        }
                        bg="white"
                        borderColor={
                          facilityErrors[idx]?.addressLine1
                            ? "red.300"
                            : "gray.300"
                        }
                        _hover={{
                          borderColor: facilityErrors[idx]?.addressLine1
                            ? "red.400"
                            : "blue.300",
                        }}
                        _focus={{
                          borderColor: facilityErrors[idx]?.addressLine1
                            ? "red.500"
                            : "blue.500",
                          shadow: facilityErrors[idx]?.addressLine1
                            ? "0 0 0 1px var(--chakra-colors-red-500)"
                            : "0 0 0 1px var(--chakra-colors-blue-500)",
                        }}
                      />
                      {facilityErrors[idx]?.addressLine1 && (
                        <Text
                          color="red.500"
                          fontSize="sm"
                          mt={1}
                          alignSelf="flex-start"
                        >
                          {facilityErrors[idx].addressLine1}
                        </Text>
                      )}

                      <Input
                        value={address.addressLine2 || ""}
                        placeholder="Address Line 2 (Optional)"
                        onChange={(e) =>
                          handleFacilityAddressChange(idx, {
                            ...address,
                            addressLine2: e.target.value,
                          })
                        }
                        bg="white"
                        borderColor={
                          facilityErrors[idx]?.addressLine2
                            ? "red.300"
                            : "gray.300"
                        }
                        _hover={{
                          borderColor: facilityErrors[idx]?.addressLine2
                            ? "red.400"
                            : "blue.300",
                        }}
                        _focus={{
                          borderColor: facilityErrors[idx]?.addressLine2
                            ? "red.500"
                            : "blue.500",
                          shadow: facilityErrors[idx]?.addressLine2
                            ? "0 0 0 1px var(--chakra-colors-red-500)"
                            : "0 0 0 1px var(--chakra-colors-blue-500)",
                        }}
                      />
                      {facilityErrors[idx]?.addressLine2 && (
                        <Text
                          color="red.500"
                          fontSize="sm"
                          mt={1}
                          alignSelf="flex-start"
                        >
                          {facilityErrors[idx].addressLine2}
                        </Text>
                      )}
                    </VStack>
                  </Box>

                  {/* Amenities */}
                  <FormControl>
                    <FormLabel
                      fontWeight="semibold"
                      fontSize="sm"
                      color="gray.600"
                    >
                      Amenities
                    </FormLabel>
                    <Box
                      bg="white"
                      borderRadius="md"
                      borderWidth={facilityErrors[idx]?.amenities ? 2 : 1}
                      borderColor={
                        facilityErrors[idx]?.amenities ? "red.300" : "gray.300"
                      }
                      p={2}
                    >
                      <ReactQuill
                        theme="snow"
                        value={address.amenities || ""}
                        onChange={(val) =>
                          handleFacilityAddressChange(idx, {
                            ...address,
                            amenities: val,
                          })
                        }
                        placeholder="Describe amenities (e.g., Swimming Pool, Gym, Courts, Parking...)"
                        style={{
                          minHeight: "80px",
                          background: "white",
                          borderRadius: "6px",
                        }}
                      />
                    </Box>
                    {facilityErrors[idx]?.amenities && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {facilityErrors[idx].amenities}
                      </Text>
                    )}
                  </FormControl>
                </VStack>
              </Box>
            ))}

            {/* Add Facility Button */}
            <Box mt={6}>
              <Button
                size={{ base: "sm", md: "md" }}
                colorScheme="teal"
                variant="outline"
                onClick={addFacilityAddress}
                leftIcon={<FiPlus />}
                w="full"
                py={{ base: 4, md: 6 }}
                borderStyle="dashed"
                borderWidth={2}
                fontSize={{ base: "sm", md: "md" }}
                _hover={{
                  bg: "teal.50",
                  borderColor: "teal.400",
                }}
              >
                Add Another Facility
              </Button>
            </Box>
          </Box>

          {/* Documents Section */}
          <Stack
            spacing={4}
            direction={{ base: "column", lg: "row" }}
            align={{ base: "stretch", lg: "flex-end" }}
          >
            <FormControl
              isInvalid={formik.errors.panNumber && formik.touched.panNumber}
              flex={1}
            >
              <FormLabel fontWeight="bold">
                PAN Number{" "}
                <Text as="span" color="red.500">
                  *
                </Text>
              </FormLabel>
              <Input
                name="panNumber"
                value={formik.values.panNumber}
                placeholder="Enter PAN No. *"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                borderColor={
                  (formik.errors.panNumber && formik.touched.panNumber) ||
                  formErrors.panNumber
                    ? "red.300"
                    : "gray.200"
                }
                _hover={{
                  borderColor:
                    (formik.errors.panNumber && formik.touched.panNumber) ||
                    formErrors.panNumber
                      ? "red.400"
                      : "gray.300",
                }}
                _focus={{
                  borderColor:
                    (formik.errors.panNumber && formik.touched.panNumber) ||
                    formErrors.panNumber
                      ? "red.500"
                      : "blue.500",
                  shadow:
                    (formik.errors.panNumber && formik.touched.panNumber) ||
                    formErrors.panNumber
                      ? "0 0 0 1px var(--chakra-colors-red-500)"
                      : "0 0 0 1px var(--chakra-colors-blue-500)",
                }}
              />
              <FormErrorMessage>{formik.errors.panNumber}</FormErrorMessage>
              {formErrors.panNumber && !formik.errors.panNumber && (
                <Text color="red.500" fontSize="sm" mt={1}>
                  {formErrors.panNumber}
                </Text>
              )}
            </FormControl>
            <Box>
              <FormLabel fontWeight="bold" mb={3}>
                PAN Image (Front & Back){" "}
                <Text as="span" color="red.500">
                  *
                </Text>
              </FormLabel>
              <Stack direction="row" spacing={4}>
                {["front", "back"].map((side) => (
                  <Box key={side}>
                    <Input
                      type="file"
                      accept="image/*"
                      display="none"
                      id={`pan-image-upload-${side}`}
                      onChange={(e) => handlePanFile(side, e)}
                    />
                    {!panFiles[side] ? (
                      <Box
                        as="label"
                        htmlFor={`pan-image-upload-${side}`}
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                        justifyContent="center"
                        w={{ base: "120px", sm: "140px", md: "150px" }}
                        h={{ base: "80px", sm: "90px", md: "100px" }}
                        border="2px dashed"
                        borderColor="gray.300"
                        borderRadius="lg"
                        bg="gray.50"
                        cursor="pointer"
                        transition="all 0.2s"
                        _hover={{ borderColor: "blue.400", bg: "blue.50" }}
                        mb={2}
                      >
                        <Icon as={FiPlus} boxSize={5} color="gray.400" mb={1} />
                        <Text fontSize="xs" color="gray.500" textAlign="center">
                          {side === "front" ? "Front" : "Back"}
                        </Text>
                      </Box>
                    ) : (
                      <VStack
                        spacing={2}
                        align="stretch"
                        w={{ base: "120px", sm: "140px", md: "150px" }}
                      >
                        <Box
                          position="relative"
                          borderWidth={2}
                          borderColor="green.200"
                          borderRadius="lg"
                          overflow="hidden"
                          bg="white"
                        >
                          <Image
                            src={URL.createObjectURL(panFiles[side])}
                            alt={`PAN ${side === "front" ? "Front" : "Back"}`}
                            w="full"
                            h={{ base: "80px", sm: "90px", md: "100px" }}
                            objectFit="cover"
                          />
                          <Box
                            position="absolute"
                            top={1}
                            right={1}
                            bg="white"
                            borderRadius="full"
                            p={1}
                            boxShadow="sm"
                          >
                            <Icon
                              as={FiTrash}
                              boxSize={4}
                              color="red.500"
                              cursor="pointer"
                              onClick={() => removePanFile(side)}
                              _hover={{ color: "red.600" }}
                            />
                          </Box>
                        </Box>
                        <Flex gap={2}>
                          <Button
                            as="label"
                            htmlFor={`pan-image-upload-${side}`}
                            size="xs"
                            variant="outline"
                            leftIcon={<FiPlus />}
                            flex={1}
                          >
                            Change
                          </Button>
                          <Button
                            size="xs"
                            colorScheme="red"
                            variant="outline"
                            leftIcon={<FiTrash />}
                            onClick={() => removePanFile(side)}
                            flex={1}
                          >
                            Remove
                          </Button>
                        </Flex>
                      </VStack>
                    )}
                    {(formik.errors.panImage && formik.touched.panImage) && (
  <Text color="red.500" fontSize="sm" mt={2}>
    {formik.errors.panImage}
  </Text>
)}
                  </Box>
                ))}
              </Stack>
            </Box>
          </Stack>

          <Stack
            spacing={4}
            direction={{ base: "column", lg: "row" }}
            align={{ base: "stretch", lg: "flex-end" }}
          >
            <FormControl
              isInvalid={
                formik.errors.aadhaarNumber && formik.touched.aadhaarNumber
              }
              flex={1}
            >
              <FormLabel fontWeight="bold">
                Aadhaar Number{" "}
                <Text as="span" color="red.500">
                  *
                </Text>
              </FormLabel>
              <Input
                name="aadhaarNumber"
                value={formik.values.aadhaarNumber}
                placeholder="Enter Aadhaar No. *"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                borderColor={
                  (formik.errors.aadhaarNumber &&
                    formik.touched.aadhaarNumber) ||
                  formErrors.aadhaarNumber
                    ? "red.300"
                    : "gray.200"
                }
                _hover={{
                  borderColor:
                    (formik.errors.aadhaarNumber &&
                      formik.touched.aadhaarNumber) ||
                    formErrors.aadhaarNumber
                      ? "red.400"
                      : "gray.300",
                }}
                _focus={{
                  borderColor:
                    (formik.errors.aadhaarNumber &&
                      formik.touched.aadhaarNumber) ||
                    formErrors.aadhaarNumber
                      ? "red.500"
                      : "blue.500",
                  shadow:
                    (formik.errors.aadhaarNumber &&
                      formik.touched.aadhaarNumber) ||
                    formErrors.aadhaarNumber
                      ? "0 0 0 1px var(--chakra-colors-red-500)"
                      : "0 0 0 1px var(--chakra-colors-blue-500)",
                }}
              />
              <FormErrorMessage>{formik.errors.aadhaarNumber}</FormErrorMessage>
              {formErrors.aadhaarNumber && !formik.errors.aadhaarNumber && (
                <Text color="red.500" fontSize="sm" mt={1}>
                  {formErrors.aadhaarNumber}
                </Text>
              )}
            </FormControl>
            <Box>
              <FormLabel fontWeight="bold" mb={3}>
                Aadhaar Image (Front & Back){" "}
                <Text as="span" color="red.500">
                  *
                </Text>
              </FormLabel>
              <Stack direction="row" spacing={4}>
                {["front", "back"].map((side) => (
                  <Box key={side}>
                    <Input
                      type="file"
                      accept="image/*"
                      display="none"
                      id={`aadhaar-image-upload-${side}`}
                      onChange={(e) => handleAadhaarFile(side, e)}
                    />
                    {!aadhaarFiles[side] ? (
                      <Box
                        as="label"
                        htmlFor={`aadhaar-image-upload-${side}`}
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                        justifyContent="center"
                        w={{ base: "120px", sm: "140px", md: "150px" }}
                        h={{ base: "80px", sm: "90px", md: "100px" }}
                        border="2px dashed"
                        borderColor="gray.300"
                        borderRadius="lg"
                        bg="gray.50"
                        cursor="pointer"
                        transition="all 0.2s"
                        _hover={{ borderColor: "blue.400", bg: "blue.50" }}
                        mb={2}
                      >
                        <Icon as={FiPlus} boxSize={5} color="gray.400" mb={1} />
                        <Text fontSize="xs" color="gray.500" textAlign="center">
                          {side === "front" ? "Front" : "Back"}
                        </Text>
                      </Box>
                    ) : (
                      <VStack
                        spacing={2}
                        align="stretch"
                        w={{ base: "120px", sm: "140px", md: "150px" }}
                      >
                        <Box
                          position="relative"
                          borderWidth={2}
                          borderColor="green.200"
                          borderRadius="lg"
                          overflow="hidden"
                          bg="white"
                        >
                          <Image
                            src={URL.createObjectURL(aadhaarFiles[side])}
                            alt={`Aadhaar ${
                              side === "front" ? "Front" : "Back"
                            }`}
                            w="full"
                            h={{ base: "80px", sm: "90px", md: "100px" }}
                            objectFit="cover"
                          />
                          <Box
                            position="absolute"
                            top={1}
                            right={1}
                            bg="white"
                            borderRadius="full"
                            p={1}
                            boxShadow="sm"
                          >
                            <Icon
                              as={FiTrash}
                              boxSize={4}
                              color="red.500"
                              cursor="pointer"
                              onClick={() => removeAadhaarFile(side)}
                              _hover={{ color: "red.600" }}
                            />
                          </Box>
                        </Box>
                        <Flex gap={2}>
                          <Button
                            as="label"
                            htmlFor={`aadhaar-image-upload-${side}`}
                            size="xs"
                            variant="outline"
                            leftIcon={<FiPlus />}
                            flex={1}
                          >
                            Change
                          </Button>
                          <Button
                            size="xs"
                            colorScheme="red"
                            variant="outline"
                            leftIcon={<FiTrash />}
                            onClick={() => removeAadhaarFile(side)}
                            flex={1}
                          >
                            Remove
                          </Button>
                        </Flex>
                      </VStack>
                    )}
                  </Box>
                ))}
              </Stack>
              {(formik.errors.aadhaarImage && formik.touched.aadhaarImage) && (
  <Text color="red.500" fontSize="sm" mt={2}>
    {formik.errors.aadhaarImage}
  </Text>
)}
            </Box>
          </Stack>

          {/* Bank Details Section */}
          <Stack spacing={4} direction={{ base: "column", md: "row" }}>
            <FormControl
              isInvalid={formik.errors.accountNo && formik.touched.accountNo}
              flex={1}
            >
              <FormLabel fontWeight="bold">
                Account No.{" "}
                <Text as="span" color="red.500">
                  *
                </Text>
              </FormLabel>
              <Input
                name="accountNo"
                value={formik.values.accountNo}
                placeholder="Enter account no. *"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                borderColor={
                  (formik.errors.accountNo && formik.touched.accountNo) ||
                  formErrors.accountNo
                    ? "red.300"
                    : "gray.200"
                }
                _hover={{
                  borderColor:
                    (formik.errors.accountNo && formik.touched.accountNo) ||
                    formErrors.accountNo
                      ? "red.400"
                      : "gray.300",
                }}
                _focus={{
                  borderColor:
                    (formik.errors.accountNo && formik.touched.accountNo) ||
                    formErrors.accountNo
                      ? "red.500"
                      : "blue.500",
                  shadow:
                    (formik.errors.accountNo && formik.touched.accountNo) ||
                    formErrors.accountNo
                      ? "0 0 0 1px var(--chakra-colors-red-500)"
                      : "0 0 0 1px var(--chakra-colors-blue-500)",
                }}
              />
              <FormErrorMessage>{formik.errors.accountNo}</FormErrorMessage>
              {formErrors.accountNo && !formik.errors.accountNo && (
                <Text color="red.500" fontSize="sm" mt={1}>
                  {formErrors.accountNo}
                </Text>
              )}
            </FormControl>
            <FormControl
              isInvalid={formik.errors.ifsc && formik.touched.ifsc}
              flex={1}
            >
              <FormLabel fontWeight="bold">
                IFSC Code{" "}
                <Text as="span" color="red.500">
                  *
                </Text>
              </FormLabel>
              <Input
                name="ifsc"
                value={formik.values.ifsc}
                placeholder="Enter IFSC code *"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                borderColor={
                  (formik.errors.ifsc && formik.touched.ifsc) || formErrors.ifsc
                    ? "red.300"
                    : "gray.200"
                }
                _hover={{
                  borderColor:
                    (formik.errors.ifsc && formik.touched.ifsc) ||
                    formErrors.ifsc
                      ? "red.400"
                      : "gray.300",
                }}
                _focus={{
                  borderColor:
                    (formik.errors.ifsc && formik.touched.ifsc) ||
                    formErrors.ifsc
                      ? "red.500"
                      : "blue.500",
                  shadow:
                    (formik.errors.ifsc && formik.touched.ifsc) ||
                    formErrors.ifsc
                      ? "0 0 0 1px var(--chakra-colors-red-500)"
                      : "0 0 0 1px var(--chakra-colors-blue-500)",
                }}
              />
              <FormErrorMessage>{formik.errors.ifsc}</FormErrorMessage>
              {formErrors.ifsc && !formik.errors.ifsc && (
                <Text color="red.500" fontSize="sm" mt={1}>
                  {formErrors.ifsc}
                </Text>
              )}
            </FormControl>
          </Stack>

          {/* Additional Information Section */}
          <FormControl
            isInvalid={formik.errors.gstNumber && formik.touched.gstNumber}
          >
            <FormLabel fontWeight="bold">
              GST Number{" "}
              <Text as="span" color="red.500">
                *
              </Text>
            </FormLabel>
            <Input
              name="gstNumber"
              value={formik.values.gstNumber}
              placeholder="Enter GST Number *"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              borderColor={
                (formik.errors.gstNumber && formik.touched.gstNumber) ||
                formErrors.gstNumber
                  ? "red.300"
                  : "gray.200"
              }
              _hover={{
                borderColor:
                  (formik.errors.gstNumber && formik.touched.gstNumber) ||
                  formErrors.gstNumber
                    ? "red.400"
                    : "gray.300",
              }}
              _focus={{
                borderColor:
                  (formik.errors.gstNumber && formik.touched.gstNumber) ||
                  formErrors.gstNumber
                    ? "red.500"
                    : "blue.500",
                shadow:
                  (formik.errors.gstNumber && formik.touched.gstNumber) ||
                  formErrors.gstNumber
                    ? "0 0 0 1px var(--chakra-colors-red-500)"
                    : "0 0 0 1px var(--chakra-colors-blue-500)",
              }}
            />
            <FormErrorMessage>{formik.errors.gstNumber}</FormErrorMessage>
            {formErrors.gstNumber && !formik.errors.gstNumber && (
              <Text color="red.500" fontSize="sm" mt={1}>
                {formErrors.gstNumber}
              </Text>
            )}
          </FormControl>
          <Box mt={8}>
            <FormLabel fontWeight="bold" mb={4}>
              Academy Images
              <Text fontSize="sm" fontWeight="normal" color="gray.600" mt={1}>
                Upload images to showcase your academy (optional)
              </Text>
            </FormLabel>

            <VStack align="stretch" spacing={4}>
              {/* Upload Button */}
              <Box>
                <Button
                  leftIcon={<FiPlus />}
                  onClick={() =>
                    document.getElementById("academy-images-upload").click()
                  }
                  colorScheme="blue"
                  variant="outline"
                  size="md"
                  _hover={{ bg: "blue.50" }}
                >
                  Add Academy Images
                </Button>
                <Input
                  id="academy-images-upload"
                  type="file"
                  display="none"
                  multiple
                  accept="image/*"
                  onChange={handleAcademyImagesUpload}
                />
              </Box>

              {/* Images Gallery */}
              {academyImages.length > 0 && (
                <Box>
                  <Text fontSize="sm" color="gray.600" mb={3}>
                    {academyImages.length} image(s) uploaded
                  </Text>
                  <Grid
                    templateColumns={{
                      base: "repeat(3, 1fr)",
                      sm: "repeat(4, 1fr)",
                      md: "repeat(6, 1fr)",
                      lg: "repeat(8, 1fr)",
                    }}
                    gap={3}
                  >
                    {academyImages.map((imageUrl, index) => (
                      <Box key={index} position="relative">
                        <Image
                          src={imageUrl}
                          alt={`Academy image ${index + 1}`}
                          boxSize={{ base: "60px", sm: "50px" }}
                          objectFit="cover"
                          borderRadius="md"
                          border="1px solid"
                          borderColor="gray.200"
                        />
                        <IconButton
                          icon={<FiTrash />}
                          size="xs"
                          colorScheme="red"
                          position="absolute"
                          top="-6px"
                          right="-6px"
                          borderRadius="full"
                          onClick={() => removeAcademyImage(index)}
                          _hover={{ bg: "red.600" }}
                        />
                      </Box>
                    ))}
                  </Grid>
                </Box>
              )}
            </VStack>
          </Box>
          <FormControl
            isInvalid={
              formik.errors.selectedCategories &&
              formik.touched.selectedCategories
            }
            ref={categoryInputRef}
            position="relative"
          >
            <FormLabel fontWeight="bold">
              Sports Categories{" "}
              <Text as="span" color="red.500">
                *
              </Text>
            </FormLabel>
            <InputGroup>
              <Input
                readOnly
                value={formik.values.selectedCategories
                  .map((cid) => categories.find((cat) => cat._id === cid)?.name)
                  .filter(Boolean)
                  .join(", ")}
                placeholder="Select sports categories *"
                onClick={() => {
                  setCategoryDropdownOpen(!categoryDropdownOpen);
                  formik.setFieldTouched("selectedCategories", true, true);
                }}
                cursor="pointer"
                bg="white"
              />
              <InputRightElement pointerEvents="none">
                <FiChevronDown />
              </InputRightElement>
            </InputGroup>
            <Flex mt={2} gap={2} wrap="wrap">
              {formik.values.selectedCategories.map((cid) => {
                const cat = categories.find((cat) => cat._id === cid);
                return cat ? (
                  <Tag
                    key={cid}
                    borderRadius="full"
                    variant="solid"
                    colorScheme="teal"
                  >
                    <Image
                      src={cat.image}
                      alt={cat.name}
                      boxSize="20px"
                      borderRadius="full"
                      mr={1}
                    />
                    <TagLabel>{cat.name}</TagLabel>
                    <TagCloseButton onClick={() => removeCategory(cid)} />
                  </Tag>
                ) : null;
              })}
            </Flex>
            {categoryDropdownOpen && (
              <Box
                position="absolute"
                zIndex={10}
                bg="white"
                borderWidth={1}
                borderRadius="md"
                boxShadow="lg"
                mb={2}
                w="100%"
                maxH="250px"
                overflowY="auto"
                p={4}
                bottom="100%"
              >
                <CheckboxGroup value={formik.values.selectedCategories}>
                  <Stack>
                    {categories.map((cat) => (
                      <Checkbox
                        key={cat._id}
                        value={cat._id}
                        isChecked={formik.values.selectedCategories.includes(
                          cat._id
                        )}
                        onChange={() => handleCategorySelect(cat._id)}
                      >
                        <Flex align="center" gap={2}>
                          <Image
                            src={cat.image}
                            alt={cat.name}
                            boxSize="24px"
                            borderRadius="full"
                          />
                          {cat.name}
                        </Flex>
                      </Checkbox>
                    ))}
                  </Stack>
                </CheckboxGroup>
              </Box>
            )}
            <FormErrorMessage>
              {formik.errors.selectedCategories}
            </FormErrorMessage>
          </FormControl>
        </Stack>
        <Button
          colorScheme="teal"
          mt={{ base: 6, md: 8 }}
          w="full"
          size={{ base: "md", md: "lg" }}
          onClick={formik.handleSubmit}
          fontWeight="bold"
       
          fontSize={{ base: "md", md: "lg" }}
        >
          Register
        </Button>
      </Box>
    </Flex>
  );
};

export default Register;