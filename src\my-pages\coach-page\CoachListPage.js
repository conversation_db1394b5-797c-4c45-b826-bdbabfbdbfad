import { <PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import Layout from "../../layout/default";
import ReactPaginate from "react-paginate";
import "../../style/pagination.css";
import { FaAngleDown } from "react-icons/fa";
import { FaCircleArrowRight } from "react-icons/fa6";
import { useMediaQuery } from "@chakra-ui/react";
import CoachListMobileView from "./CoachListMobilePage";

import {
  Button,
  Card,
  TableContainer,
  Table,
  Thead,
  Badge,
  Tr,
  Th,
  Tbody,
  Td,
  Spinner,
  Flex,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Text,
  Input,
  Box,
  Tooltip,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useToast,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  InputGroup,
  InputRightAddon,
  Select,
} from "@chakra-ui/react";
import axios from "axios";
import { useSelector } from "react-redux";
import { IoIosClose, IoMdSearch } from "react-icons/io";
import { IoFilter } from "react-icons/io5";
import { IoArrowBackCircleOutline } from "react-icons/io5";

const CoachListPage = () => {
  const [isMobile] = useMediaQuery("(max-width: 768px)");
  const [coachDetails, setCoachDetails] = useState({
    result: [],
    isLoading: false,
    error: false,
    notFound: false,
  });

  const [coachStatusChange, setCoachStatusChange] = useState({
    type: "",
    id: "",
  });

  const [coachAuthStatusChange, setCoachAuthStatusChange] = useState({
    type: "",
    id: "",
  });

  const [showSearch, setShowSearch] = useState(false);
  const [searchCoachName, setSearchCoachName] = useState("");
  // Default: 'All' for both status and authStatus (empty string matches 'All' option)
  const [selectedAuthStatus, setSelectedAuthStatus] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  const [renderMe, setRenderMe] = useState(0);

  const [isOpen3, setIsOpen3] = useState(false);
  const onClose3 = () => setIsOpen3(false);
  const onOpen3 = () => setIsOpen3(true);

  const [isOpen4, setIsOpen4] = useState(false);
  const onClose4 = () => setIsOpen4(false);
  const onOpen4 = () => setIsOpen4(true);

  const navigate = useNavigate();
  const toast = useToast();
  const token = sessionStorage?.getItem("admintoken")?.split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getCoaches = async (searchCoachName, status, authStatus) => {
    setCoachDetails({
      result: [],
      isLoading: true,
      error: false,
      notFound: false,
    });

    let queryString = "?";

    if (searchCoachName) {
      queryString += `firstName=${searchCoachName}`;
    } else {
      queryString = `?page=${currentPage}`;
    }

    if (status) {
      queryString += `${queryString ? "&" : ""}status=${status}`;
    }

    if (authStatus) {
      queryString += `${queryString ? "&" : ""}authStatus=${authStatus}`;
    }

    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/coach/${
        queryString ? `${queryString}` : ""
      }`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setCoachDetails({
          result: response.data.data,
          isLoading: false,
          error: false,
          notFound: response.data.data.length === 0 ? true : false,
        });
        setTotalPages(Math.ceil(response.data.totalResults / 25));
      })
      .catch((error) => {
        console.log(error);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response.status === 401) {
          toast({
            title: "Session expired, please login",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
        setCoachDetails({
          result: [],
          isLoading: false,
          error: true,
          notFound: false,
        });
      });
  };

  const changeCoachStatus = () => {
    let data = JSON.stringify({
      status: coachStatusChange?.type,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/coach/updateStatus/${coachStatusChange?.id}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setRenderMe((prev) => prev + 1);
        setCoachStatusChange({ type: "", id: "" });
        onClose3();
        toast({
          title: "Coach status updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setCoachStatusChange({ type: "", id: "" });
        onClose3();
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateAuthStatus = () => {
    let data = JSON.stringify({
      authStatus: coachAuthStatusChange?.type,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/coach/updateAuthStatus/${coachAuthStatusChange?.id}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setRenderMe((prev) => prev + 1);
        setCoachAuthStatusChange({ type: "", id: "" });
        onClose4();
        toast({
          title: "Coach auth status updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setCoachAuthStatusChange({ type: "", id: "" });
        onClose4();
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handlePageChange = ({ selected }) => {
    setCurrentPage(selected + 1);
  };

  useEffect(() => {
    getCoaches(searchCoachName, selectedStatus, selectedAuthStatus);
  }, [
    renderMe,
    currentPage,
    selectedAuthStatus,
    selectedStatus,
    searchCoachName,
  ]);

  return (
    <Layout title="Coach">
      {isMobile ? (
        <CoachListMobileView
          data={coachDetails.result}
          isLoading={coachDetails.isLoading}
          searchCoachName={searchCoachName}
          setSearchCoachName={setSearchCoachName}
          selectedAuthStatus={selectedAuthStatus}
          setSelectedAuthStatus={setSelectedAuthStatus}
          selectedStatus={selectedStatus}
          setSelectedStatus={setSelectedStatus}
          handleAddCoach={() => navigate("/coach-page/creation")}
          userData={userData}
          setCoachStatusChange={setCoachStatusChange}
          setCoachAuthStatusChange={setCoachAuthStatusChange}
          onOpen3={onOpen3}
          onOpen4={onOpen4}
        />
      ) : (
      <Flex
        w={"100%"}
        justifyContent={"space-between"}
        alignItems={"center"}
        mb={6}
      >
        {showSearch ? (
          <Box flexBasis={"58%"}>
            <InputGroup size="md">
              <Input
                pr="4.5rem"
                type="text"
                placeholder="Search"
                borderColor={"gray.300"}
                onChange={(e) => {
                  if (e.target.value.length >= 3) {
                    setTimeout(() => {
                      setSearchCoachName(e.target.value);
                    }, 500);
                  }
                  if (e.target.value.length === 0) {
                    setSearchCoachName("");
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    if (e.target.value.length >= 3) {
                      setSearchCoachName(e.target.value);
                    }
                  }
                }}
              />
              <InputRightAddon
                bgColor={"gray.300"}
                border={"1px"}
                borderColor={"gray.300"}
                onClick={() => {
                  setShowSearch(false);
                  setSearchCoachName("");
                }}
                cursor={"pointer"}
              >
                <IoIosClose fontSize={"24px"} />
              </InputRightAddon>
            </InputGroup>
          </Box>
        ) : (
          <Flex
            flexBasis={"59%"}
            justifyContent={"space-between"}
            alignItems={"center"}
          >
            <Flex alignItems="center" gap={0}>
              <Button
                variant="ghost"
                size={{ base: "xs", md: "sm" }}
                leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
                onClick={() => navigate(-1)}
                _hover={{ bg: "gray.100" }}
                color="gray.700"
                fontWeight="bold"
                className="p-0"
              >
              </Button>
              <Breadcrumb fontWeight="medium" fontSize="sm">
                <BreadcrumbItem isCurrentPage>
                  <BreadcrumbLink href="#">Coach</BreadcrumbLink>
                </BreadcrumbItem>
              </Breadcrumb>
            </Flex>
            <Text
              display={"flex"}
              px={4}
              justifyContent={"center"}
              alignItems={"center"}
              py={"7px"}
              border={"1px"}
              borderColor={"gray.300"}
              rounded={"md"}
              color="gray.500"
              cursor={"pointer"}
              onClick={() => setShowSearch(true)}
            >
              <IoMdSearch fontSize={"24px"} />
              <IoFilter fontSize={"22px"} ml={1} />
            </Text>
          </Flex>
        )}
        <Flex flexBasis={"40%"} justifyContent={"space-between"} gap={2}>
          <Box flexBasis={"31%"}>
            <Select
              borderColor={"gray.300"}
              cursor={"pointer"}
              bgColor={selectedStatus && "gray.300"}
              value={selectedStatus}
              onChange={(e) => {
                setSelectedStatus(e.target.value);
                setCurrentPage(1);
              }}
            >
              <option value="" disabled>Status</option>
              <option value="">All</option>
              <option value="active">Active</option>
              <option value="inactive">In-Active</option>
            </Select>
          </Box>
          <Box flexBasis={"33%"}>
            <Select
              borderColor={"gray.300"}
              cursor={"pointer"}
              bgColor={selectedAuthStatus && "gray.300"}
              value={selectedAuthStatus}
              onChange={(e) => {
                setSelectedAuthStatus(e.target.value);
                setCurrentPage(1);
              }}
            >
              <option value="" disabled>Auth Status</option>
              <option value="">All</option>
              <option value="authorized">Authorized</option>
              <option value="unauthorized">Unauthorized</option>
            </Select>
          </Box>

          <Box flexBasis={"31%"}>
            <Link to={"/coach-page/creation"}>
              <Button
                variant={"outline"}
                colorScheme="teal"
                size={"sm"}
                py={5}
                px={4}
                isDisabled={!userData?.accessScopes?.coach?.includes("write")}
              >
                Add Coach
              </Button>
            </Link>
          </Box>
        </Flex>
      </Flex>
      )}

      {!isMobile && (
        <Card>
        {!coachDetails?.isLoading && coachDetails?.error ? (
          <Flex
            justifyContent={"center"}
            alignItems={"center"}
            w={"full"}
            my={10}
          >
            <Text color={"red.500"}>
              Something went wrong please try again later...
            </Text>
          </Flex>
        ) : (
          <TableContainer
            height={`${window.innerHeight - 238}px`}
            overflowY={"scroll"}
          >
            <Table variant="simple">
              <Thead
                bgColor={"#c1eaee"}
                position={"sticky"}
                top={"0px"}
                zIndex={"99"}
              >
                <Tr bgColor={"#E2DFDF"}>
                  <Th>Name</Th>
                  <Th>Gender</Th>
                  <Th>Experience</Th>
                  <Th>Status</Th>
                  <Th>Auth Status</Th>
                  <Th>Action</Th>
                </Tr>
              </Thead>
              <Tbody>
                {coachDetails?.isLoading && !coachDetails?.error ? (
                  <Tr>
                    <Td></Td>
                    <Td></Td>
                    <Td
                      display={"flex"}
                      justifyContent={"flex-end"}
                      alignItems={"center"}
                    >
                      <Spinner />
                    </Td>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                  </Tr>
                ) : !coachDetails?.isLoading && coachDetails?.error ? (
                  <Flex
                    justifyContent={"center"}
                    alignItems={"center"}
                    w={"full"}
                    my={10}
                  >
                    <Text color={"red.500"}>
                      Something went wrong please try again later...
                    </Text>
                  </Flex>
                ) : !coachDetails?.notFound ? (
                  coachDetails?.result?.map((coach, i) => {
                    return (
                      <Tr key={i}>
                        <Td
                          style={{
                            whiteSpace: "pre-wrap",
                            wordWrap: "break-word",
                          }}
                          fontSize={"14px"}
                        >
                          {coach?.firstName + " " + coach?.lastName}
                        </Td>
                        <Td fontSize={"14px"}>
                          {coach?.gender?.toUpperCase() || "n/a"}
                        </Td>
                        <Td textAlign={"center"} fontSize={"14px"}>
                          {coach?.experience
                            ? Math.trunc(coach?.experience) + " Years"
                            : "n/a"}
                        </Td>
                        <Td fontSize={"14px"}>
                          {userData?.accessScopes?.coach?.includes("write") && (
                            <Menu>
                           
                                <MenuButton
                                  variant={"outline"}
                                  as={Button}
                                  size={"xs"}

                                  py={3}
                                  colorScheme={
                                    coach?.status === "active" ? "green" : "red"
                                  }
                                  rightIcon={<FaAngleDown />}
                                >
                                  {coach?.status?.toUpperCase()}
                                </MenuButton>
                              <MenuList>
                                <MenuItem
                                  onClick={() => {
                                    setCoachStatusChange({
                                      type: "active",
                                      id: coach?._id,
                                    });
                                    onOpen3();
                                  }}
                                >
                                  Active
                                </MenuItem>
                                <MenuItem

                                  onClick={() => {
                                    setCoachStatusChange({
                                      type: "inactive",
                                      id: coach?._id,
                                    });
                                    onOpen3();
                                  }}
                                >
                                  Inactive
                                </MenuItem>
                              </MenuList>
                            </Menu>
                          )}
                        </Td>
                        {coach?.authStatus === "authorized" ? (
                          <Td fontSize={"14px"}>
                            <Badge colorScheme="green" textAlign={"center"}>
                              Authorized
                            </Badge>
                          </Td>
                        ) : (
                          <Td fontSize={"14px"}>
                            {userData?.accessScopes?.coach?.includes(
                              "write"
                            ) && (
                              <Menu>
                                <MenuButton
                                  variant={"outline"}
                                  as={Button}
                                  size={"xs"}
                                  py={3}
                                  colorScheme={
                                    coach?.authStatus === "authorized"
                                      ? "green"
                                      : "red"
                                  }
                                  rightIcon={<FaAngleDown />}
                                  // isDisabled={coach?.status !== "active"}
                                >
                                  {coach?.authStatus?.toUpperCase()}
                                </MenuButton>
                                <MenuList>
                                  <MenuItem
                                    isDisabled={
                                      coach?.authStatus === "authorized"
                                    }
                                    onClick={() => {
                                      setCoachAuthStatusChange({
                                        type: "authorized",
                                        id: coach?._id,
                                      });
                                      onOpen4();
                                    }}
                                  >
                                    Authorized
                                  </MenuItem>
                                  {/* <MenuItem
                                    isDisabled={
                                      coach?.authStatus !== "authorized"
                                    }
                                    onClick={() => {
                                      setCoachAuthStatusChange({
                                        type: "unauthorized",
                                        id: coach?._id,
                                      });
                                      onOpen4();
                                    }}
                                  >
                                    Unauthorized
                                  </MenuItem> */}
                                </MenuList>
                              </Menu>
                            )}
                          </Td>
                        )}

                        <Td textAlign={"center"}>
                          {userData?.accessScopes?.coach?.includes("read") && (
                            <Tooltip label="Coach Details">
                              <Text
                                as={"span"}
                                fontSize={"22px"}
                                cursor={"pointer"}
                                onClick={() =>
                                  navigate(`/coach-page/details/${coach?._id}`)
                                }
                              >
                                <FaCircleArrowRight />
                              </Text>
                            </Tooltip>
                          )}
                        </Td>
                      </Tr>
                    );
                  })
                ) : (
                  <Tr>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                    <Td
                      display={"flex"}
                      justifyContent={"flex-start"}
                      alignItems={"center"}
                    >
                      <Text color={"green.500"} fontWeight={"semibold"}>
                        No result found
                      </Text>
                    </Td>
                    <Td></Td>
                    <Td></Td>
                  </Tr>
                )}
              </Tbody>
            </Table>
          </TableContainer>
        )}
      </Card>
      )}

      {/* Pagination */}
      {!coachDetails?.notFound && (
        <Flex
          justifyContent="center"
          alignItems="center"
          flexDirection={"row"}
          w={"100%"}
          mt={5}
        >
          <ReactPaginate
            previousLabel="Previous"
            nextLabel="Next"
            breakLabel="..."
            pageCount={totalPages}
            marginPagesDisplayed={2}
            pageRangeDisplayed={5}
            onPageChange={handlePageChange}
            containerClassName="pagination"
            activeClassName="active"
            forcePage={currentPage - 1}
          />
        </Flex>
      )}

      {/* Status - alert */}
      <AlertDialog isOpen={isOpen3} onClose={onClose3} isCentered>
        <AlertDialogOverlay>
          <AlertDialogContent mx={{ base: 2, md: 0 }}>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Update Coach Status
            </AlertDialogHeader>

            <AlertDialogBody>
              When updating the coach status, an automatic email will be
              triggered to notify coach the relevant changes
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button
                colorScheme="red"
                onClick={() => {
                  onClose3();
                  setCoachStatusChange({ type: "", id: "" });
                }}
              >
                Cancel
              </Button>
              <Button
                colorScheme="green"
                onClick={() => {
                  changeCoachStatus();
                }}
                ml={3}
              >
                Save Changes
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
      {/* Auth Status - alert */}
      <AlertDialog isOpen={isOpen4} onClose={onClose4} isCentered>
        <AlertDialogOverlay>
          <AlertDialogContent mx={{ base: 2, md: 0 }}>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Update Coach Auth Status
            </AlertDialogHeader>

            <AlertDialogBody>
              When updating the Auth status, an automatic email will be
              triggered to notify coach the relevant changes
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button
                colorScheme="red"
                onClick={() => {
                  onClose4();
                  setCoachAuthStatusChange({ type: "", id: "" });
                }}
              >
                Cancel
              </Button>
              <Button
                colorScheme="green"
                onClick={() => {
                  updateAuthStatus();
                }}
                ml={3}
              >
                Save Changes
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Layout>
  );
};

export default CoachListPage;
